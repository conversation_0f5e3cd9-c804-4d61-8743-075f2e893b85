# Assets Directory Structure

This directory contains all the media assets needed for the Rock Paper Scissors Flutter game.

## Directory Structure

```
assets/
├── images/              # Main game images
│   ├── start_1.png      # Start button image
│   ├── 3.png            # Countdown 3
│   ├── 2.png            # Countdown 2  
│   ├── 1.png            # Countdown 1
│   ├── ciseaux.png      # Scissors choice
│   ├── pierre.png       # Rock choice
│   ├── papier.png       # Paper choice
│   ├── you_win.png      # Win result
│   ├── you_lose.png     # Lose result
│   ├── egal.png         # Draw result
│   ├── gameover.png     # Game over screen
│   ├── play.png         # Play again button
│   ├── quit.png         # Quit button
│   ├── eddars_global.png# Welcome screen logo
│   ├── blurred_bg.png   # Background for dialogs
│   └── no_jackpot_yet.png # No jackpot indicator
├── bg_img/              # Background images
│   ├── bg_img_1.webp    # Background option 1
│   ├── bg_img_2.webp    # Background option 2
│   ├── bg_img_3.webp    # Background option 3 (default)
│   └── bg_img_4.webp    # Background option 4
├── bg_music/            # Background music
│   ├── sunrise1.mp3     # Default background music
│   ├── music2.mp3       # Alternative music option
│   └── music3.mp3       # Alternative music option
├── audio/               # Sound effects
│   ├── 3_2_1_kids.mp3   # Main countdown sound
│   ├── you_win.mp3      # Victory sound
│   ├── you_lose.mp3     # Defeat sound
│   ├── equal.mp3        # Draw sound
│   ├── gameover.mp3     # Game over sound
│   ├── jackpot_start.mp3# Jackpot start sound
│   ├── jackpot_end.mp3  # Jackpot end sound
│   └── eddars_global.mp3# Welcome screen audio
├── 3_2_go/              # Countdown sounds
│   ├── watt.mp3         # Default countdown sound
│   ├── beep.mp3         # Alternative countdown
│   └── countdown.mp3    # Alternative countdown
├── icons/               # UI icons
│   ├── settings-icon.png    # Settings button
│   ├── player.png           # Player button
│   ├── life-icon.png        # Player life icon
│   ├── baterie-plein.png    # Full battery (robot life)
│   ├── baterie-demi.png     # Half battery (robot life)
│   ├── baterie-faible.png   # Low battery (robot life)
│   ├── personne-icon.png    # Player score icon
│   └── robot-icon.png       # Robot score icon
└── fonts/               # Custom fonts
    ├── Genos-Light.ttf  # Light weight font
    └── Genos-bold.ttf   # Bold weight font
```

## Asset Migration

To migrate from the original Python/Kivy project:
1. Copy all files from the `media/` directory to their corresponding `assets/` subdirectories
2. Update any hardcoded paths in the code to use the new asset structure
3. Ensure all image formats are supported by Flutter (PNG, JPG, WebP)
4. Verify audio formats are compatible with the audioplayers plugin (MP3, WAV, AAC)

## Usage in Flutter

Assets are referenced using the `assets/` prefix:
```dart
Image.asset('assets/images/start_1.png')
AssetSource('assets/audio/you_win.mp3')
```

## Notes

- All assets should be optimized for mobile performance
- Consider providing multiple resolutions for images (1x, 2x, 3x)
- Audio files should be compressed appropriately for mobile bandwidth
- Fonts must be declared in `pubspec.yaml` to be usable