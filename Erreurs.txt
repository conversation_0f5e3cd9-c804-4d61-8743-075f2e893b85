                        },
                        {
                          "name": "camera_avfoundation",
                          "version": "0.9.21+1",
                          "kind": "transitive",
                          "source": "hosted",
                          "dependencies": [
                            "camera_platform_interface",
                            "flutter",
                            "stream_transform"
                          ],
                          "directDependencies": [
                            "camera_platform_interface",
                            "flutter",
                            "stream_transform"
                          ]
                        },
                        {
                          "name": "camera_android",
                          "version": "0.10.10+5",
                          "kind": "transitive",
                          "source": "hosted",
                          "dependencies": [
                            "camera_platform_interface",
                            "flutter",
                            "flutter_plugin_android_lifecycle",
                            "stream_transform"
                          ],
                          "directDependencies": [
                            "camera_platform_interface",
                            "flutter",
                            "flutter_plugin_android_lifecycle",
                            "stream_transform"
                          ]
                        },
                        {
                          "name": "cupertino_icons",
                          "version": "1.0.8",
                          "kind": "direct",
                          "source": "hosted",
                          "dependencies": [],
                          "directDependencies": []
                        }
                      ],
                      "sdks": [
                        {
                          "name": "<PERSON><PERSON>",
                          "version": "3.8.1"
                        },
                        {
                          "name": "Flutter",
                          "version": "3.32.5"
                        }
                      ],
                      "executables": [
                        "flutter_launcher_icons",
                        "flutter_launcher_icons:generate",
                        "flutter_launcher_icons:main"
                      ]
                    }
[  +36 ms] Found plugin audioplayers at /Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers-5.2.1/
[   +4 ms] Found plugin audioplayers_android at /Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_android-4.0.3/
[   +3 ms] Found plugin audioplayers_darwin at /Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_darwin-5.0.2/
[   +2 ms] Found plugin audioplayers_linux at /Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_linux-3.1.0/
[   +4 ms] Found plugin audioplayers_web at /Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_web-4.1.0/
[   +3 ms] Found plugin audioplayers_windows at /Users/<USER>/.pub-cache/hosted/pub.dev/audioplayers_windows-3.1.0/
[   +5 ms] Found plugin camera at /Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/
[   +2 ms] Found plugin camera_android at /Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+5/
[   +2 ms] Found plugin camera_avfoundation at /Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.21+1/
[   +5 ms] Found plugin camera_web at /Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/
[  +32 ms] Found plugin flutter_plugin_android_lifecycle at
/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.29/
[  +10 ms] Found plugin google_mlkit_barcode_scanning at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_barcode_scanning-0.14.1/
[   +3 ms] Found plugin google_mlkit_commons at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_commons-0.11.0/
[   +3 ms] Found plugin google_mlkit_digital_ink_recognition at
/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_digital_ink_recognition-0.14.1/
[   +3 ms] Found plugin google_mlkit_entity_extraction at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_entity_extraction-0.15.1/
[   +2 ms] Found plugin google_mlkit_face_detection at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_face_detection-0.13.1/
[   +2 ms] Found plugin google_mlkit_face_mesh_detection at
/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_face_mesh_detection-0.4.1/
[   +2 ms] Found plugin google_mlkit_image_labeling at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_image_labeling-0.14.1/
[   +4 ms] Found plugin google_mlkit_language_id at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_language_id-0.13.0/
[   +2 ms] Found plugin google_mlkit_object_detection at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_object_detection-0.15.0/
[   +2 ms] Found plugin google_mlkit_pose_detection at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_pose_detection-0.14.0/
[   +2 ms] Found plugin google_mlkit_selfie_segmentation at
/Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_selfie_segmentation-0.10.0/
[   +2 ms] Found plugin google_mlkit_smart_reply at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_smart_reply-0.13.0/
[   +2 ms] Found plugin google_mlkit_text_recognition at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_text_recognition-0.15.0/
[   +5 ms] Found plugin google_mlkit_translation at /Users/<USER>/.pub-cache/hosted/pub.dev/google_mlkit_translation-0.13.0/
[  +34 ms] Found plugin mediapipe_task_vision at /Users/<USER>/.pub-cache/hosted/pub.dev/mediapipe_task_vision-0.0.1/
[  +14 ms] Found plugin path_provider at /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
[   +2 ms] Found plugin path_provider_android at /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
[  +15 ms] Found plugin path_provider_foundation at /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.2/
[   +6 ms] Found plugin path_provider_linux at /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
[   +9 ms] Found plugin path_provider_windows at /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
[   +5 ms] Found plugin permission_handler at /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/
[  +30 ms] Found plugin permission_handler_android at /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/
[   +3 ms] Found plugin permission_handler_apple at /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
[   +2 ms] Found plugin permission_handler_html at /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
[   +5 ms] Found plugin permission_handler_windows at /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
[  +12 ms] Found plugin shared_preferences at /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
[   +3 ms] Found plugin shared_preferences_android at /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.11/
[   +3 ms] Found plugin shared_preferences_foundation at /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
[   +2 ms] Found plugin shared_preferences_linux at /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
[   +3 ms] Found plugin shared_preferences_web at /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
[   +3 ms] Found plugin shared_preferences_windows at /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
[  +76 ms] Generating
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/app/src/main/java/io/flutter/plugins/GeneratedPluginR
egistrant.java
[  +99 ms] executing: sysctl hw.optional.arm64
[  +20 ms] Exit code 1 from: sysctl hw.optional.arm64
[   +1 ms] sysctl: unknown oid 'hw.optional.arm64'
[        ] executing: xcrun xcodebuild -version
[ +290 ms] Exit code 0 from: xcrun xcodebuild -version
[        ] Xcode 16.2
           Build version 16C5032a
[+1102 ms] executing: /usr/bin/plutil -convert xml1 -o - /Applications/Android Studio.app/Contents/Info.plist
[  +37 ms] Exit code 0 from: /usr/bin/plutil -convert xml1 -o - /Applications/Android Studio.app/Contents/Info.plist
[        ] <?xml version="1.0" encoding="UTF-8"?>
           <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
           <plist version="1.0">
           <dict>
                <key>CFBundleDevelopmentRegion</key>
                <string>English</string>
                <key>CFBundleDocumentTypes</key>
                <array>
                        <dict>
                                <key>CFBundleTypeExtensions</key>
                                <array>
                                        <string>ipr</string>
                                </array>
                                <key>CFBundleTypeIconFile</key>
                                <string>studio.icns</string>
                                <key>CFBundleTypeName</key>
                                <string>Android Studio Project File</string>
                                <key>CFBundleTypeRole</key>
                                <string>Editor</string>
                        </dict>
                        <dict>
                                <key>CFBundleTypeExtensions</key>
                                <array>
                                        <string>*</string>
                                </array>
                                <key>CFBundleTypeName</key>
                                <string>All documents</string>
                                <key>CFBundleTypeOSTypes</key>
                                <array>
                                        <string>****</string>
                                </array>
                                <key>CFBundleTypeRole</key>
                                <string>Editor</string>
                                <key>LSTypeIsPackage</key>
                                <false/>
                        </dict>
                </array>
                <key>CFBundleExecutable</key>
                <string>studio</string>
                <key>CFBundleGetInfoString</key>
                <string>Android Studio 2024.2, build AI-242.23339.11.2421.12550806. Copyright JetBrains s.r.o., (c) 2000-2024</string>
                <key>CFBundleIconFile</key>
                <string>studio.icns</string>
                <key>CFBundleIdentifier</key>
                <string>com.google.android.studio</string>
                <key>CFBundleInfoDictionaryVersion</key>
                <string>6.0</string>
                <key>CFBundleName</key>
                <string>Android Studio</string>
                <key>CFBundlePackageType</key>
                <string>APPL</string>
                <key>CFBundleShortVersionString</key>
                <string>2024.2</string>
                <key>CFBundleURLTypes</key>
                <array>
                        <dict>
                                <key>CFBundleTypeRole</key>
                                <string>Editor</string>
                                <key>CFBundleURLName</key>
                                <string>Stacktrace</string>
                                <key>CFBundleURLSchemes</key>
                                <array>
                                        <string>idea</string>
                                </array>
                        </dict>
                </array>
                <key>CFBundleVersion</key>
                <string>AI-242.23339.11.2421.12550806</string>
                <key>LSApplicationCategoryType</key>
                <string>public.app-category.developer-tools</string>
                <key>LSArchitecturePriority</key>
                <array>
                        <string>x86_64</string>
                </array>
                <key>LSMinimumSystemVersion</key>
                <string>10.13</string>
                <key>LSRequiresNativeExecution</key>
                <string>YES</string>
                <key>NSAppleEventsUsageDescription</key>
                <string>An application in Android Studio requests the ability to send Apple events.</string>
                <key>NSBluetoothAlwaysUsageDescription</key>
                <string>An application in Android Studio requests access to Bluetooth.</string>
                <key>NSCameraUsageDescription</key>
                <string>An application in Android Studio requests access to the device's camera.</string>
                <key>NSDesktopFolderUsageDescription</key>
                <string>An application in Android Studio requests access to the user's Desktop folder.</string>
                <key>NSDocumentsFolderUsageDescription</key>
                <string>An application in Android Studio requests access to the user's Documents folder.</string>
                <key>NSDownloadsFolderUsageDescription</key>
                <string>An application in Android Studio requests access to the user's Downloads folder.</string>
                <key>NSHighResolutionCapable</key>
                <true/>
                <key>NSLocationUsageDescription</key>
                <string>An application in Android Studio requests access to the user's location information.</string>
                <key>NSMicrophoneUsageDescription</key>
                <string>An application in Android Studio requests access to the device's microphone.</string>
                <key>NSNetworkVolumesUsageDescription</key>
                <string>An application in Android Studio requests access to files on a network volume.</string>
                <key>NSRemovableVolumesUsageDescription</key>
                <string>An application in Android Studio requests access to files on a removable volume.</string>
                <key>NSSupportsAutomaticGraphicsSwitching</key>
                <true/>
           </dict>
           </plist>
[  +35 ms] executing: /Applications/Android Studio.app/Contents/jbr/Contents/Home/bin/java -version
[ +239 ms] Exit code 0 from: /Applications/Android Studio.app/Contents/jbr/Contents/Home/bin/java -version
[        ] openjdk version "21.0.3" 2024-04-16
           OpenJDK Runtime Environment (build 21.0.3+-79915915-b509.11)
           OpenJDK 64-Bit Server VM (build 21.0.3+-79915915-b509.11, mixed mode)
[  +31 ms] Top-level Gradle build file not found, skipping migration of task "clean".
[   +5 ms] executing: /Applications/Android Studio.app/Contents/jbr/Contents/Home/bin/java --version
[ +244 ms] Exit code 0 from: /Applications/Android Studio.app/Contents/jbr/Contents/Home/bin/java --version
[        ] openjdk 21.0.3 2024-04-16
           OpenJDK Runtime Environment (build 21.0.3+-79915915-b509.11)
           OpenJDK 64-Bit Server VM (build 21.0.3+-79915915-b509.11, mixed mode)
[   +4 ms] Version of Java is different than impacted version, no migration attempted.
[   +8 ms] CMake project not found, skipping support Android 15 16k page size migration.
[   +7 ms] Using gradle from /Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/gradlew.
[ +119 ms] Running Gradle task 'assembleRelease'...
[   +3 ms] executing: [/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/]
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/gradlew --full-stacktrace --info -Pverbose=true
-Ptarget-platform=android-arm,android-arm64,android-x64 -Ptarget=lib/main.dart -Pbase-application-name=android.app.Application
-Pdart-defines=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVy
L2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1
ZFUlNJT049My44LjE= -Pdart-obfuscation=false -Ptrack-widget-creation=true -Ptree-shake-icons=true assembleRelease
[+1276 ms] Initialized native services in: /Users/<USER>/.gradle/native
[   +1 ms] Initialized jansi services in: /Users/<USER>/.gradle/native
[ +233 ms] Received JVM installation metadata from '/Applications/Android Studio.app/Contents/jbr/Contents/Home':
{JAVA_HOME=/Applications/Android Studio.app/Contents/jbr/Contents/Home, JAVA_VERSION=21.0.3, JAVA_VENDOR=JetBrains s.r.o.,
RUNTIME_NAME=OpenJDK Runtime Environment, RUNTIME_VERSION=21.0.3+-79915915-b509.11, VM_NAME=OpenJDK 64-Bit Server VM,
VM_VERSION=21.0.3+-79915915-b509.11, VM_VENDOR=JetBrains s.r.o., OS_ARCH=x86_64}
[ +401 ms] The client will now receive all logging from the daemon (pid: 31753). The daemon log file:
/Users/<USER>/.gradle/daemon/8.4/daemon-31753.out.log
[        ] Starting 7th build in daemon [uptime: 53 mins 31.405 secs, performance: 99%, GC rate: 0.00/s, heap usage: 0% of 4 GiB]
[        ] Using 2 worker leases.
[        ] Now considering [/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android,
/Users/<USER>/Documents/App_py/FlutterSDK/flutter/packages/flutter_tools/gradle] as hierarchies to watch
[        ] Watching the file system is configured to be enabled if available
[        ] File system watching is active
[        ] Invalidating in-memory cache of
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/.gradle/8.4/checksums/md5-checksums.bin
[        ] Invalidating in-memory cache of
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/.gradle/8.4/checksums/sha1-checksums.bin
[        ] Invalidating in-memory cache of
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/.gradle/8.4/checksums/sha256-checksums.bin
[        ] Invalidating in-memory cache of
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/.gradle/8.4/checksums/sha512-checksums.bin
[        ] Invalidating in-memory cache of
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/.gradle/8.4/fileHashes/fileHashes.bin
[        ] Invalidating in-memory cache of
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/.gradle/8.4/fileHashes/resourceHashesCache.bin
[        ] Starting Build
[        ] Kotlin DSL script compilation (Settings/TopLevel/stage1) is not up-to-date because:
[        ]   No history is available.
[ +791 ms] Now considering [/Users/<USER>/Documents/App_py/FlutterSDK/flutter/packages/flutter_tools/gradle,
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android] as hierarchies to watch
[  +99 ms] Invalidating in-memory cache of
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/.gradle/8.4/dependencies-accessors/executionHistory.b
in
[ +205 ms] > Configure project :gradle
[        ] Evaluating project ':gradle' using build file
'/Users/<USER>/Documents/App_py/FlutterSDK/flutter/packages/flutter_tools/gradle/build.gradle.kts'.
[        ] Using Kotlin Gradle Plugin gradle81 variant
[        ] kotlin scripting plugin: created the scripting discovery configuration: kotlinScriptDef
[        ] kotlin scripting plugin: created the scripting discovery configuration: testKotlinScriptDef
[        ] WARNING: Unsupported Kotlin plugin version.
[        ] The `embedded-kotlin` and `kotlin-dsl` plugins rely on features of Kotlin `1.9.10` that might work differently than in the
requested version `1.9.20`.
[        ] file or directory '/Users/<USER>/Documents/App_py/FlutterSDK/flutter/packages/flutter_tools/gradle/src/main/java', not found
[        ] Caching disabled for Kotlin DSL accessors for project ':gradle' because:
[        ]   Build cache is disabled
[        ] Skipping Kotlin DSL accessors for project ':gradle' as it is up-to-date.
[        ] Resolve mutations for :gradle:checkKotlinGradlePluginConfigurationErrors (Thread[#209,included builds,5,main]) started.
[        ] :gradle:checkKotlinGradlePluginConfigurationErrors (Thread[#209,included builds,5,main]) started.
[        ] > Task :gradle:checkKotlinGradlePluginConfigurationErrors
[        ] Caching disabled for task ':gradle:checkKotlinGradlePluginConfigurationErrors' because:
[        ]   Build cache is disabled
[        ] Task ':gradle:checkKotlinGradlePluginConfigurationErrors' is not up-to-date because:
[        ]   Task has not declared any outputs despite executing actions.
[        ] Resolve mutations for :gradle:compileKotlin (Thread[#209,included builds,5,main]) started.
[        ] :gradle:compileKotlin (Thread[#209,included builds,5,main]) started.
[  +86 ms] > Task :gradle:compileKotlin UP-TO-DATE
[        ] Transforming gradle-api-8.4.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-ant-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-astbuilder-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-console-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-datetime-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-dateutil-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-groovydoc-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-json-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-nio-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-sql-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-templates-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-test-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming groovy-xml-3.0.17.jar with ClasspathEntrySnapshotTransform
[        ] Transforming javaparser-core-3.17.0.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-stdlib-1.9.10.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-stdlib-common-1.9.10.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-reflect-1.9.10.jar with ClasspathEntrySnapshotTransform
[        ] Transforming gradle-installation-beacon-8.4.jar with ClasspathEntrySnapshotTransform
[        ] Transforming gradle-kotlin-dsl-extensions-8.4.jar with ClasspathEntrySnapshotTransform
[        ] Transforming gradle-kotlin-dsl-8.4.jar with ClasspathEntrySnapshotTransform
[        ] Transforming gradle-kotlin-dsl-tooling-models-8.4.jar with ClasspathEntrySnapshotTransform
[        ] Transforming annotation-jvm-1.9.1.jar with ClasspathEntrySnapshotTransform
[        ] Transforming gradle-8.7.3.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-reflect-1.9.10.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlinx-serialization-json-jvm-1.4.0.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlinx-serialization-core-jvm-1.4.0.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-stdlib-jdk8-1.9.10.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-stdlib-jdk7-1.9.10.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-stdlib-1.9.24.jar with ClasspathEntrySnapshotTransform
[   +1 ms] Transforming kotlin-gradle-plugin-1.8.0-gradle76.jar with ClasspathEntrySnapshotTransform
[        ] Transforming builder-8.7.3.jar with ClasspathEntrySnapshotTransform
[        ] Transforming builder-model-8.7.3.jar with ClasspathEntrySnapshotTransform
[        ] Transforming gradle-api-8.7.3.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-gradle-plugin-model-1.8.0.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-gradle-plugin-api-1.8.0-gradle76.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-gradle-plugin-api-1.8.0.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-tooling-core-1.8.0.jar with ClasspathEntrySnapshotTransform
[        ] Transforming annotations-13.0.jar with ClasspathEntrySnapshotTransform
[   +1 ms] Transforming manifest-merger-31.7.3.jar with ClasspathEntrySnapshotTransform
[   +1 ms] Transforming zipflinger-8.7.3.jar with ClasspathEntrySnapshotTransform
[        ] Transforming apksig-8.7.3.jar with ClasspathEntrySnapshotTransform
[        ] Transforming apkzlib-8.7.3.jar with ClasspathEntrySnapshotTransform
[        ] Transforming javawriter-2.5.0.jar with ClasspathEntrySnapshotTransform
[        ] Transforming asm-9.6.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-native-utils-1.8.0.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-project-model-1.8.0.jar with ClasspathEntrySnapshotTransform
[        ] Transforming kotlin-util-io-1.8.0.jar with ClasspathEntrySnapshotTransform
[        ] Caching disabled for task ':gradle:compileKotlin' because:
[        ]   Build cache is disabled
[        ] Skipping task ':gradle:compileKotlin' as it is up-to-date.
[        ] Resolve mutations for :gradle:compileJava (Thread[#209,included builds,5,main]) started.
[        ] :gradle:compileJava (Thread[#209,included builds,5,main]) started.
[        ] > Task :gradle:compileJava NO-SOURCE
[        ] Skipping task ':gradle:compileJava' as it has no source files and no previous output files.
[        ] Resolve mutations for :gradle:compileGroovy (Thread[#209,included builds,5,main]) started.
[        ] :gradle:compileGroovy (Thread[#209,included builds,5,main]) started.
[        ] > Task :gradle:compileGroovy NO-SOURCE
[        ] Skipping task ':gradle:compileGroovy' as it has no source files and no previous output files.
[        ] Resolve mutations for :gradle:pluginDescriptors (Thread[#209,included builds,5,main]) started.
[        ] :gradle:pluginDescriptors (Thread[#209,included builds,5,main]) started.
[        ] > Task :gradle:pluginDescriptors UP-TO-DATE
[        ] Caching disabled for task ':gradle:pluginDescriptors' because:
[        ]   Build cache is disabled
[        ] Skipping task ':gradle:pluginDescriptors' as it is up-to-date.
[        ] Resolve mutations for :gradle:processResources (Thread[#209,included builds,5,main]) started.
[        ] :gradle:processResources (Thread[#209,included builds,5,main]) started.
[        ] > Task :gradle:processResources UP-TO-DATE
[        ] Caching disabled for task ':gradle:processResources' because:
[        ]   Build cache is disabled
[        ] Skipping task ':gradle:processResources' as it is up-to-date.
[        ] Resolve mutations for :gradle:classes (Thread[#209,included builds,5,main]) started.
[        ] :gradle:classes (Thread[#209,included builds,5,main]) started.
[        ] > Task :gradle:classes UP-TO-DATE
[        ] Skipping task ':gradle:classes' as it has no actions.
[        ] Resolve mutations for :gradle:jar (Thread[#209,included builds,5,main]) started.
[        ] :gradle:jar (Thread[#209,included builds,5,main]) started.
[  +77 ms] > Task :gradle:jar UP-TO-DATE
[        ] Caching disabled for task ':gradle:jar' because:
[        ]   Build cache is disabled
[        ] Skipping task ':gradle:jar' as it is up-to-date.
[ +199 ms] Kotlin DSL script compilation (Settings/TopLevel/stage2) is not up-to-date because:
[        ]   No history is available.
[+1100 ms] Settings evaluated using settings file
'/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/settings.gradle.kts'.
[        ] Projects loaded. Root project using build file
'/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/build.gradle.kts'.
[        ] Included projects: [root project 'rock_paper_scissors_flutter', project ':app', project ':audioplayers_android', project
':camera_android', project ':flutter_plugin_android_lifecycle', project ':google_mlkit_barcode_scanning', project
':google_mlkit_commons', project ':google_mlkit_digital_ink_recognition', project ':google_mlkit_entity_extraction', project
':google_mlkit_face_detection', project ':google_mlkit_face_mesh_detection', project ':google_mlkit_image_labeling', project
':google_mlkit_language_id', project ':google_mlkit_object_detection', project ':google_mlkit_pose_detection', project
':google_mlkit_selfie_segmentation', project ':google_mlkit_smart_reply', project ':google_mlkit_text_recognition', project
':google_mlkit_translation', project ':mediapipe_task_vision', project ':path_provider_android', project ':permission_handler_android',
project ':shared_preferences_android']
[ +498 ms] > Configure project :
[        ] Evaluating root project 'rock_paper_scissors_flutter' using build file
'/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/build.gradle.kts'.
[        ] Invalidating in-memory cache of
/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/.gradle/buildOutputCleanup/outputFiles.bin
[        ] Caching disabled for Kotlin DSL plugin specs accessors for classpath '0b7b3140102f0b4fbadd087ffb8ac73d' because:
[        ]   Build cache is disabled
[        ] Skipping Kotlin DSL plugin specs accessors for classpath '0b7b3140102f0b4fbadd087ffb8ac73d' as it is up-to-date.
[        ] Caching disabled for Kotlin DSL script compilation (Project/TopLevel/stage1) because:
[        ]   Build cache is disabled
[        ] Kotlin DSL script compilation (Project/TopLevel/stage1) is not up-to-date because:
[        ]   No history is available.
[        ] Caching disabled for Kotlin DSL accessors for root project 'rock_paper_scissors_flutter' because:
[        ]   Build cache is disabled
[        ] Skipping Kotlin DSL accessors for root project 'rock_paper_scissors_flutter' as it is up-to-date.
[        ] Caching disabled for Kotlin DSL script compilation (Project/TopLevel/stage2) because:
[        ]   Build cache is disabled
[        ] Kotlin DSL script compilation (Project/TopLevel/stage2) is not up-to-date because:
[        ]   No history is available.
[        ] w: file:///Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/build.gradle.kts:1:61: 'getter
for buildDir: File!' is deprecated. Deprecated in Java
[+1992 ms] > Configure project :app
[        ] Evaluating project ':app' using build file
'/Users/<USER>/Documents/App_py/rockpaperscissors/rock_paper_scissors_flutter/android/app/build.gradle.kts'.
[        ] Caching disabled for Kotlin DSL script compilation (Project/TopLevel/stage1) because:
[        ]   Build cache is disabled
[        ] Kotlin DSL script compilation (Project/TopLevel/stage1) is not up-to-date because:
[        ]   No history is available.
[        ] WARNING: The option 'android.enableD8.desugaring' is deprecated.
[        ] The current default is 'true'.
[        ] It was removed in version 7.0 of the Android Gradle plugin.
[        ] D8 desugaring is used by default, when applicable.
[        ] WARNING: The option 'android.enableBuildCache' is deprecated.
[        ] The current default is 'false'.
[        ] It was removed in version 7.0 of the Android Gradle plugin.
[        ] The Android-specific build caches were superseded by the Gradle build cache
(https://docs.gradle.org/current/userguide/build_cache.html).
[        ] Using default execution profile
[  +97 ms] Using Kotlin Gradle Plugin gradle82 variant
[ +502 ms] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[   +3 ms] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[  +89 ms] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[   +3 ms] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[   +5 ms] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Build was configured to prefer settings repositories over project repositories but repository 'maven' was added by plugin
'dev.flutter.flutter-gradle-plugin'
[        ] Caching disabled for Kotlin DSL script compilation (Project/ScriptPlugin/stage1) because:
[        ]   Build cache is disabled
[        ] Skipping Kotlin DSL script compilation (Project/ScriptPlugin/stage1) as it is up-to-date.
[ +186 ms] Caching disabled for Kotlin DSL accessors for project ':app' because:
[        ]   Build cache is disabled
[        ] Skipping Kotlin DSL accessors for project ':app' as it is up-to-date.
[        ] Caching disabled for Kotlin DSL script compilation (Project/TopLevel/stage2) because:
[        ]   Build cache is disabled
[        ] Kotlin DSL script compilation (Project/TopLevel/stage2) is not up-to-date because:
[        ]   No history is available.
[+2200 ms] Could not execute [report metric STATISTICS_COLLECT_METRICS_OVERHEAD]
[        ] Could not execute [report metric STATISTICS_COLLECT_METRICS_OVERHEAD]
[   +1 ms] FAILURE: Build failed with an exception.
[   +3 ms] * What went wrong:
[        ] A problem occurred configuring project ':app'.
[        ] > Removing unused resources requires unused code shrinking to be turned on. See
http://d.android.com/r/tools/shrink-resources.html for more information.
[        ] * Try:
[        ] > Run with --debug option to get more log output.
[        ] > Run with --scan to get full insights.
[        ] > Get more help at https://help.gradle.org.
[        ] * Exception is:
[        ] org.gradle.api.ProjectConfigurationException: A problem occurred configuring project ':app'.
[        ]      at org.gradle.configuration.project.LifecycleProjectEvaluator.wrapException(LifecycleProjectEvaluator.java:84)
[        ]      at org.gradle.configuration.project.LifecycleProjectEvaluator.addConfigurationFailure(LifecycleProjectEvaluator.java:77)
[        ]      at org.gradle.configuration.project.LifecycleProjectEvaluator.access$400(LifecycleProjectEvaluator.java:55)
[        ]      at org.gradle.configuration.project.LifecycleProjectEvaluator$NotifyAfterEvaluate.run(LifecycleProjectEvaluator.java:255)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[   +1 ms]      at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:73)
[        ]      at
org.gradle.configuration.project.LifecycleProjectEvaluator$EvaluateProject.lambda$run$0(LifecycleProjectEvaluator.java:114)
[        ]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$applyToMutableState$1(DefaultProjectStateRegistry.jav
a:406)
[        ]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$fromMutableState$2(DefaultProjectStateRegistry.java:4
29)
[        ]      at org.gradle.internal.work.DefaultWorkerLeaseService.withReplacedLocks(DefaultWorkerLeaseService.java:360)
[        ]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.fromMutableState(DefaultProjectStateRegistry.java:429)
[   +9 ms]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.applyToMutableState(DefaultProjectStateRegistry.java:405)
[        ]      at org.gradle.configuration.project.LifecycleProjectEvaluator$EvaluateProject.run(LifecycleProjectEvaluator.java:100)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:73)
[        ]      at org.gradle.configuration.project.LifecycleProjectEvaluator.evaluate(LifecycleProjectEvaluator.java:72)
[        ]      at org.gradle.api.internal.project.DefaultProject.evaluate(DefaultProject.java:788)
[        ]      at org.gradle.api.internal.project.DefaultProject.evaluate(DefaultProject.java:156)
[        ]      at
org.gradle.api.internal.project.ProjectLifecycleController.lambda$ensureSelfConfigured$2(ProjectLifecycleController.java:84)
[        ]      at org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(StateTransitionController.java:255)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:254)
[        ]      at
org.gradle.internal.model.StateTransitionController.lambda$maybeTransitionIfNotCurrentlyTransitioning$10(StateTransitionController.java:1
99)
[        ]      at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:34)
[        ]      at
org.gradle.internal.model.StateTransitionController.maybeTransitionIfNotCurrentlyTransitioning(StateTransitionController.java:195)
[        ]      at org.gradle.api.internal.project.ProjectLifecycleController.ensureSelfConfigured(ProjectLifecycleController.java:84)
[        ]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.ensureConfigured(DefaultProjectStateRegistry.java:380)
[        ]      at org.gradle.execution.TaskPathProjectEvaluator.configure(TaskPathProjectEvaluator.java:34)
[        ]      at org.gradle.execution.TaskPathProjectEvaluator.configureHierarchy(TaskPathProjectEvaluator.java:50)
[        ]      at org.gradle.configuration.DefaultProjectsPreparer.prepareProjects(DefaultProjectsPreparer.java:42)
[        ]      at org.gradle.configuration.BuildTreePreparingProjectsPreparer.prepareProjects(BuildTreePreparingProjectsPreparer.java:65)
[   +2 ms]      at
org.gradle.configuration.BuildOperationFiringProjectsPreparer$ConfigureBuild.run(BuildOperationFiringProjectsPreparer.java:52)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:73)
[        ]      at
org.gradle.configuration.BuildOperationFiringProjectsPreparer.prepareProjects(BuildOperationFiringProjectsPreparer.java:40)
[        ]      at org.gradle.initialization.VintageBuildModelController.lambda$prepareProjects$2(VintageBuildModelController.java:84)
[        ]      at org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(StateTransitionController.java:255)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:254)
[        ]      at
org.gradle.internal.model.StateTransitionController.lambda$transitionIfNotPreviously$11(StateTransitionController.java:213)
[        ]      at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:34)
[        ]      at org.gradle.internal.model.StateTransitionController.transitionIfNotPreviously(StateTransitionController.java:209)
[        ]      at org.gradle.initialization.VintageBuildModelController.prepareProjects(VintageBuildModelController.java:84)
[        ]      at org.gradle.initialization.VintageBuildModelController.prepareToScheduleTasks(VintageBuildModelController.java:71)
[        ]      at
org.gradle.internal.build.DefaultBuildLifecycleController.lambda$prepareToScheduleTasks$6(DefaultBuildLifecycleController.java:175)
[        ]      at org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(StateTransitionController.java:255)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:254)
[        ]      at org.gradle.internal.model.StateTransitionController.lambda$maybeTransition$9(StateTransitionController.java:190)
[        ]      at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:34)
[        ]      at org.gradle.internal.model.StateTransitionController.maybeTransition(StateTransitionController.java:186)
[        ]      at org.gradle.internal.build.DefaultBuildLifecycleController.prepareToScheduleTasks(DefaultBuildLifecycleController.java:173)
[        ]      at org.gradle.internal.buildtree.DefaultBuildTreeWorkPreparer.scheduleRequestedTasks(DefaultBuildTreeWorkPreparer.java:36)
[        ]      at
org.gradle.configurationcache.VintageBuildTreeWorkController$scheduleAndRunRequestedTasks$1.apply(VintageBuildTreeWorkController.kt:36)
[        ]      at
org.gradle.configurationcache.VintageBuildTreeWorkController$scheduleAndRunRequestedTasks$1.apply(VintageBuildTreeWorkController.kt:35)
[   +1 ms]      at org.gradle.composite.internal.DefaultIncludedBuildTaskGraph.withNewWorkGraph(DefaultIncludedBuildTaskGraph.java:112)
[   +1 ms]      at
org.gradle.configurationcache.VintageBuildTreeWorkController.scheduleAndRunRequestedTasks(VintageBuildTreeWorkController.kt:35)
[        ]      at
org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$scheduleAndRunTasks$1(DefaultBuildTreeLifecycleController.java:6
8)
[        ]      at
org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$runBuild$4(DefaultBuildTreeLifecycleController.java:98)
[   +1 ms]      at org.gradle.internal.model.StateTransitionController.lambda$transition$6(StateTransitionController.java:169)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
[        ]      at org.gradle.internal.model.StateTransitionController.lambda$transition$7(StateTransitionController.java:169)
[        ]      at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:44)
[        ]      at org.gradle.internal.model.StateTransitionController.transition(StateTransitionController.java:169)
[        ]      at org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.runBuild(DefaultBuildTreeLifecycleController.java:95)
[        ]      at
org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.scheduleAndRunTasks(DefaultBuildTreeLifecycleController.java:68)
[        ]      at
org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.scheduleAndRunTasks(DefaultBuildTreeLifecycleController.java:63)
[        ]      at org.gradle.tooling.internal.provider.ExecuteBuildActionRunner.run(ExecuteBuildActionRunner.java:31)
[        ]      at org.gradle.launcher.exec.ChainingBuildActionRunner.run(ChainingBuildActionRunner.java:35)
[        ]      at org.gradle.internal.buildtree.ProblemReportingBuildActionRunner.run(ProblemReportingBuildActionRunner.java:49)
[        ]      at org.gradle.launcher.exec.BuildOutcomeReportingBuildActionRunner.run(BuildOutcomeReportingBuildActionRunner.java:65)
[        ]      at org.gradle.tooling.internal.provider.FileSystemWatchingBuildActionRunner.run(FileSystemWatchingBuildActionRunner.java:140)
[        ]      at org.gradle.launcher.exec.BuildCompletionNotifyingBuildActionRunner.run(BuildCompletionNotifyingBuildActionRunner.java:41)
[        ]      at
org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.lambda$execute$0(RootBuildLifecycleBuildActionExecutor.java:40)
[        ]      at org.gradle.composite.internal.DefaultRootBuildState.run(DefaultRootBuildState.java:123)
[        ]      at org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.execute(RootBuildLifecycleBuildActionExecutor.java:40)
[        ]      at org.gradle.internal.buildtree.InitDeprecationLoggingActionExecutor.execute(InitDeprecationLoggingActionExecutor.java:62)
[        ]      at org.gradle.internal.buildtree.InitProblems.execute(InitProblems.java:38)
[        ]      at org.gradle.internal.buildtree.DefaultBuildTreeContext.execute(DefaultBuildTreeContext.java:40)
[        ]      at
org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.lambda$execute$0(BuildTreeLifecycleBuildActionExecutor.java:65)
[        ]      at org.gradle.internal.buildtree.BuildTreeState.run(BuildTreeState.java:58)
[        ]      at org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.execute(BuildTreeLifecycleBuildActionExecutor.java:65)
[        ]      at org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$3.call(RunAsBuildOperationBuildActionExecutor.java:61)
[        ]      at org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$3.call(RunAsBuildOperationBuildActionExecutor.java:57)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:199)
[   +3 ms]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationExecutor.call(DefaultBuildOperationExecutor.java:78)
[        ]      at org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor.execute(RunAsBuildOperationBuildActionExecutor.java:57)
[        ]      at
org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.lambda$execute$0(RunAsWorkerThreadBuildActionExecutor.java:36)
[   +1 ms]      at org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:264)
[   +1 ms]      at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:128)
[        ]      at org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.execute(RunAsWorkerThreadBuildActionExecutor.java:36)
[        ]      at
org.gradle.tooling.internal.provider.continuous.ContinuousBuildActionExecutor.execute(ContinuousBuildActionExecutor.java:110)
[        ]      at org.gradle.tooling.internal.provider.SubscribableBuildActionExecutor.execute(SubscribableBuildActionExecutor.java:64)
[        ]      at org.gradle.internal.session.DefaultBuildSessionContext.execute(DefaultBuildSessionContext.java:46)
[        ]      at
org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter$ActionImpl.apply(BuildSessionLifecycleBuildActionExecuter.j
ava:92)
[        ]      at
org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter$ActionImpl.apply(BuildSessionLifecycleBuildActionExecuter.j
ava:80)
[   +3 ms]      at org.gradle.internal.session.BuildSessionState.run(BuildSessionState.java:69)
[        ]      at
org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter.execute(BuildSessionLifecycleBuildActionExecuter.java:62)
[        ]      at
org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter.execute(BuildSessionLifecycleBuildActionExecuter.java:41)
[        ]      at
org.gradle.tooling.internal.provider.StartParamsValidatingActionExecuter.execute(StartParamsValidatingActionExecuter.java:64)
[        ]      at
org.gradle.tooling.internal.provider.StartParamsValidatingActionExecuter.execute(StartParamsValidatingActionExecuter.java:32)
[        ]      at
org.gradle.tooling.internal.provider.SessionFailureReportingActionExecuter.execute(SessionFailureReportingActionExecuter.java:51)
[        ]      at
org.gradle.tooling.internal.provider.SessionFailureReportingActionExecuter.execute(SessionFailureReportingActionExecuter.java:39)
[        ]      at org.gradle.tooling.internal.provider.SetupLoggingActionExecuter.execute(SetupLoggingActionExecuter.java:47)
[        ]      at org.gradle.tooling.internal.provider.SetupLoggingActionExecuter.execute(SetupLoggingActionExecuter.java:31)
[        ]      at org.gradle.launcher.daemon.server.exec.ExecuteBuild.doBuild(ExecuteBuild.java:65)
[        ]      at org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(BuildCommandOnly.java:37)
[   +7 ms]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.WatchForDisconnection.execute(WatchForDisconnection.java:39)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.ResetDeprecationLogger.execute(ResetDeprecationLogger.java:29)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.RequestStopIfSingleUsedDaemon.execute(RequestStopIfSingleUsedDaemon.java:35)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.ForwardClientInput$2.create(ForwardClientInput.java:78)
[        ]      at org.gradle.launcher.daemon.server.exec.ForwardClientInput$2.create(ForwardClientInput.java:75)
[        ]      at org.gradle.util.internal.Swapper.swap(Swapper.java:38)
[        ]      at org.gradle.launcher.daemon.server.exec.ForwardClientInput.execute(ForwardClientInput.java:75)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.LogAndCheckHealth.execute(LogAndCheckHealth.java:64)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.LogToClient.doBuild(LogToClient.java:63)
[        ]      at org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(BuildCommandOnly.java:37)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment.doBuild(EstablishBuildEnvironment.java:84)
[        ]      at org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(BuildCommandOnly.java:37)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy$1.run(StartBuildOrRespondWithBusy.java:52)
[        ]      at org.gradle.launcher.daemon.server.DaemonStateCoordinator$1.run(DaemonStateCoordinator.java:297)
[        ]      at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
[        ]      at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:47)
[        ]      at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
[        ]      at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
[        ]      at java.base/java.lang.Thread.run(Unknown Source)
[        ] Caused by: com.android.builder.errors.EvalIssueException: Removing unused resources requires unused code shrinking to be
turned on. See http://d.android.com/r/tools/shrink-resources.html for more information.
[        ]      at com.android.builder.errors.IssueReporter.reportError(IssueReporter.kt:114)
[   +2 ms]      at com.android.builder.errors.IssueReporter.reportError$default(IssueReporter.kt:110)
[        ]      at
com.android.build.api.component.impl.features.AndroidResourcesCreationConfigImpl.getUseResourceShrinker(AndroidResourcesCreationConfigImp
l.kt:91)
[   +1 ms]      at com.android.build.gradle.internal.TaskManager.createMergeResourcesTask(TaskManager.kt:466)
[        ]      at com.android.build.gradle.internal.AbstractAppTaskManager.createMergeResourcesTasks(AbstractAppTaskManager.java:251)
[        ]      at com.android.build.gradle.internal.AbstractAppTaskManager.createCommonTasks(AbstractAppTaskManager.java:125)
[        ]      at com.android.build.gradle.internal.tasks.ApplicationTaskManager.doCreateTasksForVariant(ApplicationTaskManager.kt:80)
[        ]      at com.android.build.gradle.internal.VariantTaskManager.createTasksForVariant(VariantTaskManager.kt:189)
[        ]      at com.android.build.gradle.internal.VariantTaskManager.createTasks(VariantTaskManager.kt:125)
[        ]      at com.android.build.gradle.internal.plugins.BasePlugin.createAndroidTasks(BasePlugin.kt:722)
[        ]      at com.android.build.gradle.internal.plugins.BasePlugin$createTasks$2$1.call(BasePlugin.kt:597)
[        ]      at
com.android.build.gradle.internal.profile.NoOpAnalyticsConfiguratorService.recordBlock(NoOpAnalyticsConfiguratorService.kt:53)
[        ]      at com.android.build.gradle.internal.plugins.BasePlugin$createTasks$2.accept(BasePlugin.kt:592)
[        ]      at com.android.build.gradle.internal.plugins.BasePlugin$createTasks$2.accept(BasePlugin.kt:589)
[        ]      at com.android.build.gradle.internal.crash.CrashReporting$afterEvaluate$1.execute(crash_reporting.kt:37)
[        ]      at com.android.build.gradle.internal.crash.CrashReporting$afterEvaluate$1.execute(crash_reporting.kt:35)
[        ]      at
org.gradle.configuration.internal.DefaultUserCodeApplicationContext$CurrentApplication$1.execute(DefaultUserCodeApplicationContext.java:1
23)
[        ]      at
org.gradle.configuration.internal.DefaultListenerBuildOperationDecorator$BuildOperationEmittingAction$1.run(DefaultListenerBuildOperation
Decorator.java:171)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:73)
[        ]      at
org.gradle.configuration.internal.DefaultListenerBuildOperationDecorator$BuildOperationEmittingAction.execute(DefaultListenerBuildOperati
onDecorator.java:168)
[        ]      at org.gradle.internal.event.BroadcastDispatch$ActionInvocationHandler.dispatch(BroadcastDispatch.java:99)
[        ]      at org.gradle.internal.event.BroadcastDispatch$ActionInvocationHandler.dispatch(BroadcastDispatch.java:87)
[        ]      at org.gradle.internal.event.AbstractBroadcastDispatch.dispatch(AbstractBroadcastDispatch.java:43)
[        ]      at org.gradle.internal.event.BroadcastDispatch$SingletonDispatch.dispatch(BroadcastDispatch.java:268)
[        ]      at org.gradle.internal.event.BroadcastDispatch$SingletonDispatch.dispatch(BroadcastDispatch.java:170)
[        ]      at org.gradle.internal.event.AbstractBroadcastDispatch.dispatch(AbstractBroadcastDispatch.java:83)
[        ]      at org.gradle.internal.event.AbstractBroadcastDispatch.dispatch(AbstractBroadcastDispatch.java:69)
[        ]      at org.gradle.internal.event.BroadcastDispatch$CompositeDispatch.dispatch(BroadcastDispatch.java:381)
[        ]      at org.gradle.internal.event.BroadcastDispatch$CompositeDispatch.dispatch(BroadcastDispatch.java:272)
[        ]      at org.gradle.internal.event.ListenerBroadcast.dispatch(ListenerBroadcast.java:148)
[        ]      at org.gradle.internal.event.ListenerBroadcast.dispatch(ListenerBroadcast.java:37)
[        ]      at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:94)
[        ]      at jdk.proxy1/jdk.proxy1.$Proxy62.afterEvaluate(Unknown Source)
[        ]      at
org.gradle.configuration.project.LifecycleProjectEvaluator$NotifyAfterEvaluate$1.execute(LifecycleProjectEvaluator.java:247)
[        ]      at
org.gradle.configuration.project.LifecycleProjectEvaluator$NotifyAfterEvaluate$1.execute(LifecycleProjectEvaluator.java:244)
[        ]      at org.gradle.api.internal.project.DefaultProject.stepEvaluationListener(DefaultProject.java:1495)
[        ]      at org.gradle.configuration.project.LifecycleProjectEvaluator$NotifyAfterEvaluate.run(LifecycleProjectEvaluator.java:253)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:73)
[        ]      at
org.gradle.configuration.project.LifecycleProjectEvaluator$EvaluateProject.lambda$run$0(LifecycleProjectEvaluator.java:114)
[        ]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$applyToMutableState$1(DefaultProjectStateRegistry.jav
a:406)
[        ]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$fromMutableState$2(DefaultProjectStateRegistry.java:4
29)
[        ]      at org.gradle.internal.work.DefaultWorkerLeaseService.withReplacedLocks(DefaultWorkerLeaseService.java:360)
[        ]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.fromMutableState(DefaultProjectStateRegistry.java:429)
[        ]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.applyToMutableState(DefaultProjectStateRegistry.java:405)
[        ]      at org.gradle.configuration.project.LifecycleProjectEvaluator$EvaluateProject.run(LifecycleProjectEvaluator.java:100)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:73)
[        ]      at org.gradle.configuration.project.LifecycleProjectEvaluator.evaluate(LifecycleProjectEvaluator.java:72)
[        ]      at org.gradle.api.internal.project.DefaultProject.evaluate(DefaultProject.java:788)
[        ]      at org.gradle.api.internal.project.DefaultProject.evaluate(DefaultProject.java:156)
[        ]      at
org.gradle.api.internal.project.ProjectLifecycleController.lambda$ensureSelfConfigured$2(ProjectLifecycleController.java:84)
[   +1 ms]      at org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(StateTransitionController.java:255)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:254)
[        ]      at
org.gradle.internal.model.StateTransitionController.lambda$maybeTransitionIfNotCurrentlyTransitioning$10(StateTransitionController.java:1
99)
[        ]      at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:34)
[        ]      at
org.gradle.internal.model.StateTransitionController.maybeTransitionIfNotCurrentlyTransitioning(StateTransitionController.java:195)
[        ]      at org.gradle.api.internal.project.ProjectLifecycleController.ensureSelfConfigured(ProjectLifecycleController.java:84)
[        ]      at
org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.ensureConfigured(DefaultProjectStateRegistry.java:380)
[        ]      at org.gradle.execution.TaskPathProjectEvaluator.configure(TaskPathProjectEvaluator.java:34)
[        ]      at org.gradle.execution.TaskPathProjectEvaluator.configureHierarchy(TaskPathProjectEvaluator.java:50)
[        ]      at org.gradle.configuration.DefaultProjectsPreparer.prepareProjects(DefaultProjectsPreparer.java:42)
[        ]      at org.gradle.configuration.BuildTreePreparingProjectsPreparer.prepareProjects(BuildTreePreparingProjectsPreparer.java:65)
[        ]      at
org.gradle.configuration.BuildOperationFiringProjectsPreparer$ConfigureBuild.run(BuildOperationFiringProjectsPreparer.java:52)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationExecutor.run(DefaultBuildOperationExecutor.java:73)
[        ]      at
org.gradle.configuration.BuildOperationFiringProjectsPreparer.prepareProjects(BuildOperationFiringProjectsPreparer.java:40)
[        ]      at org.gradle.initialization.VintageBuildModelController.lambda$prepareProjects$2(VintageBuildModelController.java:84)
[        ]      at org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(StateTransitionController.java:255)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:254)
[        ]      at
org.gradle.internal.model.StateTransitionController.lambda$transitionIfNotPreviously$11(StateTransitionController.java:213)
[        ]      at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:34)
[        ]      at org.gradle.internal.model.StateTransitionController.transitionIfNotPreviously(StateTransitionController.java:209)
[        ]      at org.gradle.initialization.VintageBuildModelController.prepareProjects(VintageBuildModelController.java:84)
[        ]      at org.gradle.initialization.VintageBuildModelController.prepareToScheduleTasks(VintageBuildModelController.java:71)
[        ]      at
org.gradle.internal.build.DefaultBuildLifecycleController.lambda$prepareToScheduleTasks$6(DefaultBuildLifecycleController.java:175)
[        ]      at org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(StateTransitionController.java:255)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:254)
[   +2 ms]      at org.gradle.internal.model.StateTransitionController.lambda$maybeTransition$9(StateTransitionController.java:190)
[        ]      at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:34)
[        ]      at org.gradle.internal.model.StateTransitionController.maybeTransition(StateTransitionController.java:186)
[        ]      at org.gradle.internal.build.DefaultBuildLifecycleController.prepareToScheduleTasks(DefaultBuildLifecycleController.java:173)
[        ]      at org.gradle.internal.buildtree.DefaultBuildTreeWorkPreparer.scheduleRequestedTasks(DefaultBuildTreeWorkPreparer.java:36)
[        ]      at
org.gradle.configurationcache.VintageBuildTreeWorkController$scheduleAndRunRequestedTasks$1.apply(VintageBuildTreeWorkController.kt:36)
[        ]      at
org.gradle.configurationcache.VintageBuildTreeWorkController$scheduleAndRunRequestedTasks$1.apply(VintageBuildTreeWorkController.kt:35)
[        ]      at org.gradle.composite.internal.DefaultIncludedBuildTaskGraph.withNewWorkGraph(DefaultIncludedBuildTaskGraph.java:112)
[        ]      at
org.gradle.configurationcache.VintageBuildTreeWorkController.scheduleAndRunRequestedTasks(VintageBuildTreeWorkController.kt:35)
[        ]      at
org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$scheduleAndRunTasks$1(DefaultBuildTreeLifecycleController.java:6
8)
[        ]      at
org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$runBuild$4(DefaultBuildTreeLifecycleController.java:98)
[        ]      at org.gradle.internal.model.StateTransitionController.lambda$transition$6(StateTransitionController.java:169)
[        ]      at org.gradle.internal.model.StateTransitionController.doTransition(StateTransitionController.java:266)
[        ]      at org.gradle.internal.model.StateTransitionController.lambda$transition$7(StateTransitionController.java:169)
[        ]      at org.gradle.internal.work.DefaultSynchronizer.withLock(DefaultSynchronizer.java:44)
[        ]      at org.gradle.internal.model.StateTransitionController.transition(StateTransitionController.java:169)
[        ]      at org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.runBuild(DefaultBuildTreeLifecycleController.java:95)
[        ]      at
org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.scheduleAndRunTasks(DefaultBuildTreeLifecycleController.java:68)
[        ]      at
org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.scheduleAndRunTasks(DefaultBuildTreeLifecycleController.java:63)
[        ]      at org.gradle.tooling.internal.provider.ExecuteBuildActionRunner.run(ExecuteBuildActionRunner.java:31)
[        ]      at org.gradle.launcher.exec.ChainingBuildActionRunner.run(ChainingBuildActionRunner.java:35)
[        ]      at org.gradle.internal.buildtree.ProblemReportingBuildActionRunner.run(ProblemReportingBuildActionRunner.java:49)
[        ]      at org.gradle.launcher.exec.BuildOutcomeReportingBuildActionRunner.run(BuildOutcomeReportingBuildActionRunner.java:65)
[        ]      at org.gradle.tooling.internal.provider.FileSystemWatchingBuildActionRunner.run(FileSystemWatchingBuildActionRunner.java:140)
[        ]      at org.gradle.launcher.exec.BuildCompletionNotifyingBuildActionRunner.run(BuildCompletionNotifyingBuildActionRunner.java:41)
[        ]      at
org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.lambda$execute$0(RootBuildLifecycleBuildActionExecutor.java:40)
[        ]      at org.gradle.composite.internal.DefaultRootBuildState.run(DefaultRootBuildState.java:123)
[        ]      at org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.execute(RootBuildLifecycleBuildActionExecutor.java:40)
[        ]      at org.gradle.internal.buildtree.InitDeprecationLoggingActionExecutor.execute(InitDeprecationLoggingActionExecutor.java:62)
[        ]      at org.gradle.internal.buildtree.InitProblems.execute(InitProblems.java:38)
[        ]      at org.gradle.internal.buildtree.DefaultBuildTreeContext.execute(DefaultBuildTreeContext.java:40)
[        ]      at
org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.lambda$execute$0(BuildTreeLifecycleBuildActionExecutor.java:65)
[   +3 ms]      at org.gradle.internal.buildtree.BuildTreeState.run(BuildTreeState.java:58)
[        ]      at org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.execute(BuildTreeLifecycleBuildActionExecutor.java:65)
[   +2 ms]      at org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$3.call(RunAsBuildOperationBuildActionExecutor.java:61)
[        ]      at org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$3.call(RunAsBuildOperationBuildActionExecutor.java:57)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
[        ]      at
org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:199)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
[        ]      at org.gradle.internal.operations.DefaultBuildOperationExecutor.call(DefaultBuildOperationExecutor.java:78)
[        ]      at org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor.execute(RunAsBuildOperationBuildActionExecutor.java:57)
[        ]      at
org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.lambda$execute$0(RunAsWorkerThreadBuildActionExecutor.java:36)
[        ]      at org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:264)
[        ]      at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:128)
[        ]      at org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.execute(RunAsWorkerThreadBuildActionExecutor.java:36)
[        ]      at
org.gradle.tooling.internal.provider.continuous.ContinuousBuildActionExecutor.execute(ContinuousBuildActionExecutor.java:110)
[        ]      at org.gradle.tooling.internal.provider.SubscribableBuildActionExecutor.execute(SubscribableBuildActionExecutor.java:64)
[        ]      at org.gradle.internal.session.DefaultBuildSessionContext.execute(DefaultBuildSessionContext.java:46)
[        ]      at
org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter$ActionImpl.apply(BuildSessionLifecycleBuildActionExecuter.j
ava:92)
[        ]      at
org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter$ActionImpl.apply(BuildSessionLifecycleBuildActionExecuter.j
ava:80)
[        ]      at org.gradle.internal.session.BuildSessionState.run(BuildSessionState.java:69)
[        ]      at
org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter.execute(BuildSessionLifecycleBuildActionExecuter.java:62)
[        ]      at
org.gradle.tooling.internal.provider.BuildSessionLifecycleBuildActionExecuter.execute(BuildSessionLifecycleBuildActionExecuter.java:41)
[        ]      at
org.gradle.tooling.internal.provider.StartParamsValidatingActionExecuter.execute(StartParamsValidatingActionExecuter.java:64)
[        ]      at
org.gradle.tooling.internal.provider.StartParamsValidatingActionExecuter.execute(StartParamsValidatingActionExecuter.java:32)
[        ]      at
org.gradle.tooling.internal.provider.SessionFailureReportingActionExecuter.execute(SessionFailureReportingActionExecuter.java:51)
[        ]      at
org.gradle.tooling.internal.provider.SessionFailureReportingActionExecuter.execute(SessionFailureReportingActionExecuter.java:39)
[        ]      at org.gradle.tooling.internal.provider.SetupLoggingActionExecuter.execute(SetupLoggingActionExecuter.java:47)
[        ]      at org.gradle.tooling.internal.provider.SetupLoggingActionExecuter.execute(SetupLoggingActionExecuter.java:31)
[        ]      at org.gradle.launcher.daemon.server.exec.ExecuteBuild.doBuild(ExecuteBuild.java:65)
[        ]      at org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(BuildCommandOnly.java:37)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.WatchForDisconnection.execute(WatchForDisconnection.java:39)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.ResetDeprecationLogger.execute(ResetDeprecationLogger.java:29)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.RequestStopIfSingleUsedDaemon.execute(RequestStopIfSingleUsedDaemon.java:35)
[   +1 ms]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.ForwardClientInput$2.create(ForwardClientInput.java:78)
[        ]      at org.gradle.launcher.daemon.server.exec.ForwardClientInput$2.create(ForwardClientInput.java:75)
[        ]      at org.gradle.util.internal.Swapper.swap(Swapper.java:38)
[        ]      at org.gradle.launcher.daemon.server.exec.ForwardClientInput.execute(ForwardClientInput.java:75)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.LogAndCheckHealth.execute(LogAndCheckHealth.java:64)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.LogToClient.doBuild(LogToClient.java:63)
[        ]      at org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(BuildCommandOnly.java:37)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment.doBuild(EstablishBuildEnvironment.java:84)
[        ]      at org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(BuildCommandOnly.java:37)
[        ]      at org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed(DaemonCommandExecution.java:104)
[        ]      at org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy$1.run(StartBuildOrRespondWithBusy.java:52)
[        ]      at org.gradle.launcher.daemon.server.DaemonStateCoordinator$1.run(DaemonStateCoordinator.java:297)
[        ]      at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
[        ]      at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:47)
[        ]      at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
[        ]      at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
[        ]      at java.base/java.lang.Thread.run(Unknown Source)
[        ] BUILD FAILED in 9s
[   +3 ms] 5 actionable tasks: 1 executed, 4 up-to-date
[        ] Watched directory hierarchies: [/Users/<USER>/Documents/App_py/FlutterSDK/flutter/packages/flutter_tools/gradle]
[ +320 ms] Running Gradle task 'assembleRelease'... (completed in 10.6s)
[ +125 ms] "flutter apk" took 24’544ms.
[  +17 ms] executing: sw_vers -productName
[  +41 ms] Exit code 0 from: sw_vers -productName
[        ] macOS
[        ] executing: sw_vers -productVersion
[  +41 ms] Exit code 0 from: sw_vers -productVersion
[        ] 14.5
[        ] executing: sw_vers -buildVersion
[  +43 ms] Exit code 0 from: sw_vers -buildVersion
[        ] 23F79
[        ] executing: uname -m
[  +27 ms] Exit code 0 from: uname -m
[        ] x86_64
[ +179 ms] Gradle task assembleRelease failed with exit code 1
[        ] 
           #0      throwToolExit (package:flutter_tools/src/base/common.dart:34:3)
           #1      AndroidGradleBuilder.buildGradleApp (package:flutter_tools/src/android/gradle.dart:582:7)
           <asynchronous suspension>
           #2      AndroidGradleBuilder.buildApk (package:flutter_tools/src/android/gradle.dart:242:5)
           <asynchronous suspension>
           #3      BuildApkCommand.runCommand (package:flutter_tools/src/commands/build_apk.dart:141:5)
           <asynchronous suspension>
           #4      FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1563:27)
           <asynchronous suspension>
           #5      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
           <asynchronous suspension>
           #6      CommandRunner.runCommand (package:args/command_runner.dart:212:13)
           <asynchronous suspension>
           #7      FlutterCommandRunner.runCommand.<anonymous closure>
(package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
           <asynchronous suspension>
           #8      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
           <asynchronous suspension>
           #9      FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
           <asynchronous suspension>
           #10     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)
           <asynchronous suspension>
           #11     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
           <asynchronous suspension>
           #12     main (package:flutter_tools/executable.dart:102:3)
           <asynchronous suspension>
           
           
[   +2 ms] Running 1 shutdown hook
[   +8 ms] Shutdown hooks complete
[ +271 ms] exiting with code 1
noury@MacBook-Pro-de-NourEddine rock_paper_scissors_flutter % 