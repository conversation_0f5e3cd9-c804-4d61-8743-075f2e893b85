name: rock_paper_scissors_flutter
description: Flutter version of the Rock Paper Scissors gesture recognition game
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI and Navigation
  cupertino_icons: ^1.0.2
  
  # Camera for gesture recognition
  camera: ^0.10.5+5
  
  # Audio playback
  audioplayers: ^5.2.1
  
  # File storage and JSON handling
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  
  # Animation and UI utilities
  flutter_animate: ^4.2.0+1
  
  # State management
  provider: ^6.1.1
  
  # Image handling
  flutter_svg: ^2.0.9
  
  # Permissions for camera access
  permission_handler: ^11.1.0
  
  # Video processing (if needed for advanced gesture detection)
  image: ^4.1.3
  hand_signature: ^3.1.0+2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/
    - assets/images/
    - assets/audio/
    - assets/fonts/
    - assets/bg_img/
    - assets/bg_music/
    - assets/3_2_go/
    - assets/icons/
    # MediaPipe gesture recognizer model
    - assets/models/gesture_recognizer.task


  # Custom fonts
  fonts:
    - family: Genos
      fonts:
        - asset: assets/fonts/Genos-Light.ttf
          weight: 300
        - asset: assets/fonts/Genos-bold.ttf
          weight: 700

# Flutter Launcher Icons Configuration
flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assets/images/logo.png"
    background_color: "#ffffff"
    theme_color: "#000000"
  image_path: "assets/images/logo.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/logo.png"


