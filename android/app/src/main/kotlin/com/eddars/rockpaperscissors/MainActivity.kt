package com.eddars.rockpaperscissors

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.EventChannel
import com.eddars.rockpaperscissors.gesture.MpGestureManager

class MainActivity : FlutterActivity() {

    private var gestureManager: MpGestureManager? = null

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        android.util.Log.d("MainActivity", "🚀 INIT: Configuring Flutter engine...")

        // Initialize gesture manager - SEULEMENT MEDIAPIPE
        gestureManager = MpGestureManager(this)
        android.util.Log.d("MainActivity", "✅ INIT: Gesture manager created")

        // Set up method channel for gesture control
        android.util.Log.d("MainActivity", "🔌 CHANNEL: Setting up mp_gesture/methods channel...")
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, "mp_gesture/methods").setMethodCallHandler { call, result ->
            when (call.method) {
                "start" -> {
                    val minScore = call.argument<Double>("minScore") ?: 0.65
                    gestureManager?.setMinScore(minScore)
                    gestureManager?.start()
                    result.success(null)
                }
                "stop" -> {
                    gestureManager?.stop()
                    result.success(null)
                }
                else -> result.notImplemented()
            }
        }

        // Set up event channel for gesture results
        android.util.Log.d("MainActivity", "🔌 CHANNEL: Setting up mp_gesture/events channel...")
        EventChannel(flutterEngine.dartExecutor.binaryMessenger, "mp_gesture/events").setStreamHandler(gestureManager)

        android.util.Log.d("MainActivity", "✅ INIT: All channels configured successfully")
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        gestureManager?.onRequestPermissionsResult(requestCode, grantResults)
    }


}


