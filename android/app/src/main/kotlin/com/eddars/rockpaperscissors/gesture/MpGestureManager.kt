package com.eddars.rockpaperscissors.gesture

import android.Manifest
import android.content.pm.PackageManager
import android.util.Size
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.common.util.concurrent.ListenableFuture
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.EventChannel

// Imports minimaux pour test
import android.graphics.Bitmap

class MpGestureManager(private val activity: FlutterActivity) : EventChannel.StreamHandler {

    private var cameraProviderFuture: ListenableFuture<ProcessCameraProvider>? = null
    private var analysis: ImageAnalysis? = null
    private var classifier: Any? = null // Temporaire pour test
    private var sink: EventChannel.EventSink? = null
    private var minScore: Double = 0.65

    fun start() {
        android.util.Log.d("MpGestureManager", "🚀 START: Method called")
        try {
            ensurePermissionAndStart()
        } catch (e: Exception) {
            android.util.Log.e("MpGestureManager", "❌ START ERROR: ${e.message}")
        }
    }

    fun setMinScore(minScore: Double) {
        this.minScore = minScore
    }

    fun stop() {
        try {
            val provider = cameraProviderFuture?.get()
            provider?.unbindAll()
        } catch (_: Throwable) {}
        analysis = null
        classifier = null
    }

    override fun onListen(arguments: Any?, events: EventChannel.EventSink) {
        sink = events
    }

    override fun onCancel(arguments: Any?) {
        sink = null
    }

    fun onRequestPermissionsResult(requestCode: Int, grantResults: IntArray) {
        if (requestCode == 9001 && grantResults.isNotEmpty() &&
            grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            startCameraAndRecognizer()
        }
    }

    private fun ensurePermissionAndStart() {
        try {
            android.util.Log.d("MpGestureManager", "🔐 PERMISSION: Checking camera permission...")
            val granted = ContextCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED
            if (!granted) {
                android.util.Log.w("MpGestureManager", "⚠️ PERMISSION: Camera permission not granted, requesting...")
                ActivityCompat.requestPermissions(activity, arrayOf(Manifest.permission.CAMERA), 9001)
                return
            }
            android.util.Log.d("MpGestureManager", "✅ PERMISSION: Camera permission granted, starting camera...")
            startCameraAndRecognizer()
        } catch (e: Exception) {
            android.util.Log.e("MpGestureManager", "❌ PERMISSION ERROR: ${e.message}")
        }
    }

    private fun startCameraAndRecognizer() {
        // SKIP MODEL LOADING - juste tester la caméra d'abord
        android.util.Log.d("MpGestureManager", "📁 SKIP: Model loading skipped for testing")

        // CAMÉRA FRONTALE + NOTRE MODÈLE
        cameraProviderFuture = ProcessCameraProvider.getInstance(activity)
        cameraProviderFuture?.addListener({
            val provider = cameraProviderFuture?.get() ?: return@addListener
            provider.unbindAll()

            // Configuration améliorée pour la caméra frontale
            val analyzer = ImageAnalysis.Builder()
                .setTargetResolution(Size(640, 480))
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888)
                .build()

            analyzer.setAnalyzer(activity.mainExecutor) { imageProxy ->
                analyzeFrame(imageProxy)
            }

            // Vérifier la disponibilité de la caméra frontale
            val cameraSelector = try {
                if (provider.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA)) {
                    android.util.Log.d("MpGestureManager", "✅ Caméra frontale disponible")
                    CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                        .build()
                } else {
                    android.util.Log.w("MpGestureManager", "⚠️ Caméra frontale non disponible, utilisation de la caméra arrière")
                    CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                        .build()
                }
            } catch (e: Exception) {
                android.util.Log.e("MpGestureManager", "❌ Erreur lors de la vérification de la caméra: ${e.message}")
                CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                    .build()
            }

            analysis = analyzer

            try {
                provider.bindToLifecycle(activity, cameraSelector, analyzer)
                android.util.Log.d("MpGestureManager", "✅ Caméra liée avec succès")
            } catch (e: Exception) {
                android.util.Log.e("MpGestureManager", "❌ Erreur lors de la liaison de la caméra: ${e.message}")
            }
        }, activity.mainExecutor)
    }

    private fun analyzeFrame(image: ImageProxy) {
        try {
            // SIMPLE TEST - juste envoyer un geste aléatoire
            val testGestures = listOf("rock", "paper", "scissors")
            val randomGesture = testGestures.random()

            android.util.Log.d("MpGestureManager", "📷 Frame reçu - envoi: $randomGesture")

            sink?.success(mapOf(
                "label" to randomGesture,
                "score" to 0.8,
                "hand" to "Camera",
                "ts" to System.currentTimeMillis()
            ))

        } catch (e: Throwable) {
            android.util.Log.e("MpGestureManager", "Erreur: ${e.message}")
        } finally {
            image.close()
        }
    }



    // Cette méthode n'est plus utilisée avec TensorFlow Lite
    // La classification se fait directement dans analyzeFrame

    // Copy Flutter asset to a real file path, so createFromFile can read it.





}


