// android/app/build.gradle.kts  — multi‑ABI for debug, arm64‑only for release
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.eddars.rockpaperscissors"
    compileSdk = 36
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions { jvmTarget = "11" }

    defaultConfig {
        applicationId = "com.eddars.rockpaperscissors"
        minSdk = 21
        targetSdk = 36
        versionCode = 1
        versionName = "1.0"
    }

    buildTypes {
        debug {
            ndk {
                // Include ALL architectures for maximum compatibility
                abiFilters += listOf("arm64-v8a", "armeabi-v7a", "x86_64", "x86")
            }
            isMinifyEnabled = false
            isShrinkResources = false
            isDebuggable = true
        }
        release {
            // keep APK small for release on physical devices
            ndk {
                abiFilters.clear()
                abiFilters += listOf("arm64-v8a")
            }
            signingConfig = signingConfigs.getByName("debug")
            isMinifyEnabled = true  // Garder la minification
            isShrinkResources = true // Garder la réduction des ressources
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }

    packaging {
        jniLibs {
            useLegacyPackaging = true
        }
        resources {
            pickFirsts += listOf(
                "META-INF/LICENSE.md",
                "META-INF/LICENSE-notice.md"
            )
        }
    }


}

flutter { source = "../.." }

dependencies {
    // Dépendances de base pour la caméra
    implementation("androidx.camera:camera-core:1.3.4")
    implementation("androidx.camera:camera-camera2:1.3.4")
    implementation("androidx.camera:camera-lifecycle:1.3.4")
    implementation("androidx.camera:camera-view:1.3.4")

    // Pas de dépendances externes pour test
}
