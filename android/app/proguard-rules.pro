# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# Flutter specific rules
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# Camera plugin rules
-keep class androidx.camera.** { *; }
-dontwarn androidx.camera.**

# Audio players rules
-keep class xyz.luan.audioplayers.** { *; }
-dontwarn xyz.luan.audioplayers.**

# MediaPipe native plugin rules (mp_gesture)
-keep class com.google.mediapipe.** { *; }
-dontwarn com.google.mediapipe.**
-keep class org.tensorflow.lite.** { *; }
-dontwarn org.tensorflow.lite.**

# Permission handler rules
-keep class com.baseflow.permissionhandler.** { *; }
-dontwarn com.baseflow.permissionhandler.**

# Shared preferences rules
-keep class io.flutter.plugins.sharedpreferences.** { *; }
-dontwarn io.flutter.plugins.sharedpreferences.**

# Path provider rules
-keep class io.flutter.plugins.pathprovider.** { *; }
-dontwarn io.flutter.plugins.pathprovider.**

# Image processing rules
-keep class androidx.exifinterface.** { *; }
-dontwarn androidx.exifinterface.**

# Flutter Animate rules
-keep class dev.flutter.plugins.flutter_animate.** { *; }
-dontwarn dev.flutter.plugins.flutter_animate.**

# Hand signature rules
-keep class io.flutter.plugins.handsignature.** { *; }
-dontwarn io.flutter.plugins.handsignature.**

# Flutter SVG rules
-keep class com.pichillilorenzo.flutter_svg.** { *; }
-dontwarn com.pichillilorenzo.flutter_svg.**

# Custom gesture recognition plugin rules
-keep class com.eddars.rockpaperscissors.gesture.** { *; }
-dontwarn com.eddars.rockpaperscissors.gesture.**

# Camera X rules
-keep class androidx.camera.** { *; }
-dontwarn androidx.camera.**

# Google Common utilities (used by CameraX)
-keep class com.google.common.** { *; }
-dontwarn com.google.common.**
