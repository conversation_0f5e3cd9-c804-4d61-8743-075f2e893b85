/**
Copyright 2022 The Flutter Authors
Use of this source code is governed by a BSD-style license that can be
found in the LICENSE file or at https://developers.google.com/open-source/licenses/bsd.
**/

:root {
    /* Perfetto CSS variable overrides. */
    --details-content-height: 180px! important;
    --selection-fill-color: #13b9fd4d !important;
    --selection-stroke-color: #13b9fd !important;
    --sidebar-width: 0px! important;
    --topbar-height: 36px! important;
}

body {
  overscroll-behavior-x: none !important;
}

.topbar .omnibox {
    height: 28px !important;
    border-radius: 14px !important;
    border: solid 1px !important;
    border-width: thin !important;
}

.topbar .omnibox::before {
    font-size: 18px !important;
    margin-top: 4px !important;
    margin-left: 5px !important;
    padding-top: 0px !important;
}

.topbar .omnibox input {
    height: 26px !important;
    font-size: 16px !important;
}

.topbar .omnibox .stepthrough {
    align-items: center !important;
    height: 28px !important;
}

.topbar .omnibox.command-mode {
    margin-left: 4px !important;
    margin-right: 4px !important;
}

.overview-timeline {
    height: 50px !important;
}

.query-table, .query-table thead td {
  font-size: 13px !important;
}

.query-table tbody tr {
  font-size: 12px !important;
}

.modal-dialog > header {
  height: 16px !important;
}

.modal-dialog main {
  font-size: 14px !important;
  margin-top: 0px !important;
  margin-bottom: 0px !important;
  line-height: 1.25 !important;
}

.help h2 {
  padding-top: 8px !important;
}

.keycap {
  line-height: 14px !important;
}

::-webkit-scrollbar {
  width: 10px !important;
  height: 10px !important;
}

::-webkit-scrollbar-track {
  border-radius: 2px !important;
}

::-webkit-scrollbar-thumb {
  border-radius: 5px !important;
}

.pf-header-bar {
  padding-top: 2px 2px 2px 8px !important;
}

.pf-header-title,
.pf-menu .pf-menu-item {
  font-size: 14px !important;
}
