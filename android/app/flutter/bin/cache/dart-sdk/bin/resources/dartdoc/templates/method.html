{{ >head }}

<div
    id="dartdoc-main-content"
    class="main-content"
    data-above-sidebar="{{ aboveSidebarPath }}"
    data-below-sidebar="{{ belowSidebarPath }}">
  {{ #self }}
    <div>{{ >source_link }}<h1><span class="kind-method">{{{ nameWithGenerics }}}</span> {{ fullkind }} {{ >feature_set }}</h1></div>
  {{ /self }}

  {{ #method }}
    <section class="multi-line-signature">
      {{ >callable_multiline }}
      {{ >attributes }}
    </section>
    {{ >documentation }}

    {{ >source_code }}

  {{ /method }}
  </div> <!-- /.main-content -->

  <div id="dartdoc-sidebar-left" class="sidebar sidebar-offcanvas-left">
    {{ >search_sidebar }}
    {{ #isParentExtension }}
    <h5>{{ parent.name }} {{ parent.kind }} on {{{ parentAsExtension.extendedElement.linkedName }}}</h5>
    {{ /isParentExtension }}
    {{ ^isParentExtension }}
    <h5>{{ parent.name }} {{ parent.kind }}</h5>
    {{ /isParentExtension }}
    <div id="dartdoc-sidebar-left-content"></div>
  </div><!--/.sidebar-offcanvas-->

  <div id="dartdoc-sidebar-right" class="sidebar sidebar-offcanvas-right">
</div><!--/.sidebar-offcanvas-->

{{ >footer }}
