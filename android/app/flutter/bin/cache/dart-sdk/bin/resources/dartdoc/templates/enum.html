{{>head}}

<div
    id="dartdoc-main-content"
    class="main-content"
    data-above-sidebar="{{ aboveSidebarPath }}"
    data-below-sidebar="{{ belowSidebarPath }}">
  {{ #self }}
    <div>{{ >source_link }}
      <h1>
        <span class="kind-enum">{{{ nameWithGenerics }}}</span>
        {{ kind }} {{ >feature_set }} {{ >categorization }}
      </h1>
    </div>
  {{ /self }}

  {{ #eNum }}
    {{ >documentation }}

    {{ #hasModifiers }}
      <section>
        <dl class="dl-horizontal">
          {{ >super_chain }}
          {{ >interfaces }}
          {{ >mixed_in_types }}
          {{ >available_extensions }}
          {{ >container_annotations }}
        </dl>
      </section>
    {{ /hasModifiers }}

    {{ #hasPublicEnumValues }}
      <section class="summary offset-anchor" id="values">
        <h2>Values</h2>

        <dl class="properties">
          {{ #publicEnumValues }}
            {{ >constant }}
          {{ /publicEnumValues }}
        </dl>
      </section>
    {{ /hasPublicEnumValues }}

    {{ >instance_fields }}
    {{ >instance_methods }}
    {{ >instance_operators }}
    {{ >static_properties }}
    {{ >static_methods }}
    {{ >static_constants }}
  {{ /eNum }}
</div><!-- /.main-content -->

<div id="dartdoc-sidebar-left" class="sidebar sidebar-offcanvas-left">
  {{ >search_sidebar }}
  <h5>{{ parent.name }} {{ parent.kind }}</h5>
  <div id="dartdoc-sidebar-left-content"></div>
</div>

<div id="dartdoc-sidebar-right" class="sidebar sidebar-offcanvas-right">
</div><!-- /.sidebar-offcanvas -->

{{ >footer }}
