dartdoc:
  categoryOrder: ["Core", "VM", "Web", "Web (Legacy)"]
  categories:
    'Web':
      external:
        - name: 'package:web'
          url: https://pub.dev/documentation/web/latest/
          docs: >-
            This package exposes browser APIs. It's intended to replace
            dart:html and similar Dart SDK libraries. It will support access to
            browser APIs from Dart code compiled to either JavaScript or
            WebAssembly.
  linkToSource:
    root: '.'
    uriTemplate: 'https://github.com/dart-lang/sdk/blob/54588cb8088890ea08fe1a31b95efe478a4609b5/sdk/%f%#L%l%'
  errors:
    # Default errors of dartdoc:
    - duplicate-file
    - invalid-parameter
    - no-defining-library-found
    - tool-error
    - unresolved-export
    # Warnings that are elevated to errors:
    - ambiguous-doc-reference
    - ambiguous-reexport
    - broken-link
    - category-order-gives-missing-package-name
    - deprecated
    - ignored-canonical-for
    - missing-from-search-index
    - no-canonical-found
    - no-documentable-libraries
    - no-library-level-docs
    - not-implemented
    - orphaned-file
    - reexported-private-api-across-packages
    # - unknown-directive  # Disabled due to https://github.com/dart-lang/dartdoc/issues/2353
    - unknown-file
    - unknown-macro
    - unresolved-doc-reference
  header:
    - ../../../tools/bots/dartdoc_header.html
  footer:
    - ../../../tools/bots/dartdoc_footer.html
  footerText:
    - ../../../tools/bots/dartdoc_footer_text.html
