# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the flutter/packages/flutter/test_fixes/README.md
# file for instructions on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for InteractiveViewer from the widgets library. *
version: 1
transforms:
  # Change made in https://github.com/flutter/flutter/pull/109014
  - title: "Migrate to 'panAxis'"
    date: 2024-02-22
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      constructor: ''
      inClass: 'InteractiveViewer'
    variables:
      alignPanAxis:
        kind: 'fragment'
        value: 'arguments[alignPanAxis]'
      panAxis:
        kind: 'fragment'
        value: 'arguments[panAxis]'
    oneOf:
      - if: "panAxis == '' && alignPanAxis == 'true'"
        changes:
          - kind: 'addParameter'
            index: 0
            name: 'panAxis'
            style: optional_named
            argumentValue:
              expression: 'PanAxis.aligned'
              requiredIf: "alignPanAxis == 'true'"
          - kind: 'removeParameter'
            name: 'alignPanAxis'
      - if: "panAxis == '' && alignPanAxis == 'false'"
        changes:
          - kind: 'addParameter'
            index: 0
            name: 'panAxis'
            style: optional_named
            argumentValue:
              expression: 'PanAxis.free'
              requiredIf: "alignPanAxis == 'false'"
          - kind: 'removeParameter'
            name: 'alignPanAxis'
      - if: "alignPanAxis != ''"
        changes:
          - kind: 'removeParameter'
            name: 'alignPanAxis'

  # Change made in https://github.com/flutter/flutter/pull/109014
  - title: "Migrate to 'panAxis'"
    date: 2024-02-22
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      constructor: 'builder'
      inClass: 'InteractiveViewer'
    variables:
      alignPanAxis:
        kind: 'fragment'
        value: 'arguments[alignPanAxis]'
      panAxis:
        kind: 'fragment'
        value: 'arguments[panAxis]'
    oneOf:
      - if: "panAxis == '' && alignPanAxis == 'true'"
        changes:
          - kind: 'addParameter'
            index: 0
            name: 'panAxis'
            style: optional_named
            argumentValue:
              expression: 'PanAxis.aligned'
              requiredIf: "alignPanAxis == 'true'"
          - kind: 'removeParameter'
            name: 'alignPanAxis'
      - if: "panAxis == '' && alignPanAxis == 'false'"
        changes:
          - kind: 'addParameter'
            index: 0
            name: 'panAxis'
            style: optional_named
            argumentValue:
              expression: 'PanAxis.free'
              requiredIf: "alignPanAxis == 'false'"
          - kind: 'removeParameter'
            name: 'alignPanAxis'
      - if: "alignPanAxis != ''"
        changes:
          - kind: 'removeParameter'
            name: 'alignPanAxis'

# Before adding a new fix: read instructions at the top of this file.
