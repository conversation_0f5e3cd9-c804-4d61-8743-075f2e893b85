# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the flutter/packages/flutter/test_fixes/README.md
# file for instructions on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for ColorScheme from the Material library. *
#   For fixes to
#     * AppBar: fix_app_bar.yaml
#     * AppBarTheme: fix_app_bar_theme.yaml
#     * Material (general): fix_material.yaml
#     * SliverAppBar: fix_sliver_app_bar.yaml
#     * TextTheme: fix_text_theme.yaml
#     * ThemeData: fix_theme_data.yaml
#     * WidgetState: fix_widget_state.yaml
version: 1
transforms:

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate background"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: ''
      inClass: 'ColorScheme'
    oneOf:
      - if: "surface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'background'
            newName: 'surface'
      - if: "surface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'background'
    variables:
      surface:
        kind: 'fragment'
        value: 'arguments[surface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate onBackground"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: ''
      inClass: 'ColorScheme'
    oneOf:
      - if: "onSurface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'onBackground'
            newName: 'onSurface'
      - if: "onSurface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'onBackground'
    variables:
      onSurface:
        kind: 'fragment'
        value: 'arguments[onSurface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate surfaceVariant"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: ''
      inClass: 'ColorScheme'
    oneOf:
      - if: "surfaceContainerHighest == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'surfaceVariant'
            newName: 'surfaceContainerHighest'
      - if: "surfaceContainerHighest != ''"
        changes:
          - kind: 'removeParameter'
            name: 'surfaceVariant'
    variables:
      surfaceContainerHighest:
        kind: 'fragment'
        value: 'arguments[surfaceContainerHighest]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate background"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'light'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'background'
            newName: 'surface'
      - if: "surface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'background'
    variables:
      surface:
        kind: 'fragment'
        value: 'arguments[surface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate onBackground"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'light'
      inClass: 'ColorScheme'
    oneOf:
      - if: "onSurface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'onBackground'
            newName: 'onSurface'
      - if: "onSurface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'onBackground'
    variables:
      onSurface:
        kind: 'fragment'
        value: 'arguments[onSurface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate surfaceVariant"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'light'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surfaceContainerHighest == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'surfaceVariant'
            newName: 'surfaceContainerHighest'
      - if: "surfaceContainerHighest != ''"
        changes:
          - kind: 'removeParameter'
            name: 'surfaceVariant'
    variables:
      surfaceContainerHighest:
        kind: 'fragment'
        value: 'arguments[surfaceContainerHighest]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate background"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'dark'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'background'
            newName: 'surface'
      - if: "surface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'background'
    variables:
      surface:
        kind: 'fragment'
        value: 'arguments[surface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate onBackground"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'dark'
      inClass: 'ColorScheme'
    oneOf:
      - if: "onSurface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'onBackground'
            newName: 'onSurface'
      - if: "onSurface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'onBackground'
    variables:
      onSurface:
        kind: 'fragment'
        value: 'arguments[onSurface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate surfaceVariant"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'dark'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surfaceContainerHighest == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'surfaceVariant'
            newName: 'surfaceContainerHighest'
      - if: "surfaceContainerHighest != ''"
        changes:
          - kind: 'removeParameter'
            name: 'surfaceVariant'
    variables:
      surfaceContainerHighest:
        kind: 'fragment'
        value: 'arguments[surfaceContainerHighest]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate background"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'highContrastLight'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'background'
            newName: 'surface'
      - if: "surface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'background'
    variables:
      surface:
        kind: 'fragment'
        value: 'arguments[surface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate onBackground"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'highContrastLight'
      inClass: 'ColorScheme'
    oneOf:
      - if: "onSurface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'onBackground'
            newName: 'onSurface'
      - if: "onSurface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'onBackground'
    variables:
      onSurface:
        kind: 'fragment'
        value: 'arguments[onSurface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate surfaceVariant"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'highContrastLight'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surfaceContainerHighest == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'surfaceVariant'
            newName: 'surfaceContainerHighest'
      - if: "surfaceContainerHighest != ''"
        changes:
          - kind: 'removeParameter'
            name: 'surfaceVariant'
    variables:
      surfaceContainerHighest:
        kind: 'fragment'
        value: 'arguments[surfaceContainerHighest]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate background"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'highContrastDark'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'background'
            newName: 'surface'
      - if: "surface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'background'
    variables:
      surface:
        kind: 'fragment'
        value: 'arguments[surface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate onBackground"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'highContrastDark'
      inClass: 'ColorScheme'
    oneOf:
      - if: "onSurface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'onBackground'
            newName: 'onSurface'
      - if: "onSurface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'onBackground'
    variables:
      onSurface:
        kind: 'fragment'
        value: 'arguments[onSurface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate surfaceVariant"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      constructor: 'highContrastDark'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surfaceContainerHighest == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'surfaceVariant'
            newName: 'surfaceContainerHighest'
      - if: "surfaceContainerHighest != ''"
        changes:
          - kind: 'removeParameter'
            name: 'surfaceVariant'
    variables:
      surfaceContainerHighest:
        kind: 'fragment'
        value: 'arguments[surfaceContainerHighest]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate background"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      method: 'copyWith'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'background'
            newName: 'surface'
      - if: "surface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'background'
    variables:
      surface:
        kind: 'fragment'
        value: 'arguments[surface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate onBackground"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      method: 'copyWith'
      inClass: 'ColorScheme'
    oneOf:
      - if: "onSurface == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'onBackground'
            newName: 'onSurface'
      - if: "onSurface != ''"
        changes:
          - kind: 'removeParameter'
            name: 'onBackground'
    variables:
      onSurface:
        kind: 'fragment'
        value: 'arguments[onSurface]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate surfaceVariant"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      method: 'copyWith'
      inClass: 'ColorScheme'
    oneOf:
      - if: "surfaceContainerHighest == ''"
        changes:
          - kind: 'renameParameter'
            oldName: 'surfaceVariant'
            newName: 'surfaceContainerHighest'
      - if: "surfaceContainerHighest != ''"
        changes:
          - kind: 'removeParameter'
            name: 'surfaceVariant'
    variables:
      surfaceContainerHighest:
        kind: 'fragment'
        value: 'arguments[surfaceContainerHighest]'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate 'background' to 'surface'"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      getter: 'background'
      inClass: 'ColorScheme'
    changes:
      - kind: 'rename'
        newName: 'surface'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate 'onBackground' to 'onSurface'"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      getter: 'onBackground'
      inClass: 'ColorScheme'
    changes:
      - kind: 'rename'
        newName: 'onSurface'

  # Changes made in https://github.com/flutter/flutter/pull/138521
  - title: "Migrate 'surfaceVariant' to 'surfaceContainerHighest'"
    date: 2023-12-04
    element:
      uris: [ 'material.dart' ]
      getter: 'surfaceVariant'
      inClass: 'ColorScheme'
    changes:
      - kind: 'rename'
        newName: 'surfaceContainerHighest'

  # Changes made in https://github.com/flutter/flutter/pull/93427
  - title: "Remove 'primaryVariant' and 'secondaryVariant'"
    date: 2021-11-19
    element:
      uris: [ 'material.dart' ]
      constructor: ''
      inClass: 'ColorScheme'
    changes:
      - kind: 'removeParameter'
        name: 'primaryVariant'
      - kind: 'removeParameter'
        name: 'secondaryVariant'

  # Changes made in https://github.com/flutter/flutter/pull/93427
  - title: "Remove 'primaryVariant' and 'secondaryVariant'"
    date: 2021-11-19
    element:
      uris: [ 'material.dart' ]
      constructor: 'light'
      inClass: 'ColorScheme'
    changes:
      - kind: 'removeParameter'
        name: 'primaryVariant'
      - kind: 'removeParameter'
        name: 'secondaryVariant'

  # Changes made in https://github.com/flutter/flutter/pull/93427
  - title: "Remove 'primaryVariant' and 'secondaryVariant'"
    date: 2021-11-19
    element:
      uris: [ 'material.dart' ]
      constructor: 'dark'
      inClass: 'ColorScheme'
    changes:
      - kind: 'removeParameter'
        name: 'primaryVariant'
      - kind: 'removeParameter'
        name: 'secondaryVariant'

  # Changes made in https://github.com/flutter/flutter/pull/93427
  - title: "Remove 'primaryVariant' and 'secondaryVariant'"
    date: 2021-11-19
    element:
      uris: [ 'material.dart' ]
      constructor: 'highContrastLight'
      inClass: 'ColorScheme'
    changes:
      - kind: 'removeParameter'
        name: 'primaryVariant'
      - kind: 'removeParameter'
        name: 'secondaryVariant'

  # Changes made in https://github.com/flutter/flutter/pull/93427
  - title: "Remove 'primaryVariant' and 'secondaryVariant'"
    date: 2021-11-19
    element:
      uris: [ 'material.dart' ]
      constructor: 'highContrastDark'
      inClass: 'ColorScheme'
    changes:
      - kind: 'removeParameter'
        name: 'primaryVariant'
      - kind: 'removeParameter'
        name: 'secondaryVariant'

  # Changes made in https://github.com/flutter/flutter/pull/93427
  - title: "Remove 'primaryVariant' and 'secondaryVariant'"
    date: 2021-11-19
    element:
      uris: [ 'material.dart' ]
      method: 'copyWith'
      inClass: 'ColorScheme'
    changes:
      - kind: 'removeParameter'
        name: 'primaryVariant'
      - kind: 'removeParameter'
        name: 'secondaryVariant'

  # Changes made in https://github.com/flutter/flutter/pull/93427
  - title: "Migrate 'primaryVariant' to 'primaryContainer'"
    date: 2021-11-19
    element:
      uris: [ 'material.dart' ]
      getter: 'primaryVariant'
      inClass: 'ColorScheme'
    changes:
      - kind: 'rename'
        newName: 'primaryContainer'

  # Changes made in https://github.com/flutter/flutter/pull/93427
  - title: "Migrate 'secondaryVariant' to 'secondaryContainer'"
    date: 2021-11-19
    element:
      uris: [ 'material.dart' ]
      getter: 'secondaryVariant'
      inClass: 'ColorScheme'
    changes:
      - kind: 'rename'
        newName: 'secondaryContainer'

# Before adding a new fix: read instructions at the top of this file.
