import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'pro_hand_gesture_detector.dart';

/// Comprehensive diagnostics and error reporting system for MediaPipe gesture detection
/// Provides detailed analysis, troubleshooting, and reporting capabilities
class GestureDiagnosticsService {
  static const String _tag = 'GestureDiagnosticsService';
  
  // Singleton instance
  static GestureDiagnosticsService? _instance;
  static GestureDiagnosticsService get instance {
    _instance ??= GestureDiagnosticsService._internal();
    return _instance!;
  }
  
  GestureDiagnosticsService._internal();
  
  // Diagnostic data collection
  final List<DiagnosticEvent> _events = [];
  final Map<String, dynamic> _systemInfo = {};
  final Map<String, int> _errorCounts = {};
  Timer? _periodicReportTimer;
  bool _enableLogging = true;
  
  // Performance tracking
  DateTime? _sessionStartTime;
  int _totalInitializationAttempts = 0;
  int _successfulInitializations = 0;
  int _totalDetectionSessions = 0;
  int _successfulDetectionSessions = 0;
  Duration _totalDetectionTime = Duration.zero;
  
  /// Initialize the diagnostics service
  Future<void> initialize() async {
    _sessionStartTime = DateTime.now();
    await _collectSystemInfo();
    _startPeriodicReporting();
    _logEvent('DiagnosticsServiceInitialized', {'timestamp': DateTime.now().toIso8601String()});
  }
  
  /// Collect comprehensive system information
  Future<void> _collectSystemInfo() async {
    try {
      _systemInfo.clear();
      _systemInfo['platform'] = Platform.operatingSystem;
      _systemInfo['platform_version'] = Platform.operatingSystemVersion;
      _systemInfo['dart_version'] = Platform.version;
      _systemInfo['is_android'] = Platform.isAndroid;
      _systemInfo['is_ios'] = Platform.isIOS;
      _systemInfo['session_start'] = DateTime.now().toIso8601String();
      
      if (Platform.isAndroid) {
        // Get Android-specific information
        _systemInfo['android_info'] = await _getAndroidDeviceInfo();
      }
      
      debugPrint('🔍 $_tag: System information collected: ${_systemInfo.length} properties');
    } catch (e) {
      _logEvent('SystemInfoError', {'error': e.toString()});
    }
  }
  
  /// Get Android device information for architecture diagnostics
  Future<Map<String, dynamic>> _getAndroidDeviceInfo() async {
    final androidInfo = <String, dynamic>{};
    try {
      // This would require device_info_plus package for full implementation
      // For now, we'll use available Platform information
      androidInfo['supported_abis'] = 'Available via device_info_plus';
      androidInfo['architecture_info'] = 'Requires device_info_plus package';
    } catch (e) {
      androidInfo['error'] = 'Failed to get Android info: $e';
    }
    return androidInfo;
  }
  
  /// Start periodic diagnostic reporting
  void _startPeriodicReporting() {
    _periodicReportTimer?.cancel();
    _periodicReportTimer = Timer.periodic(Duration(minutes: 5), (timer) {
      _generatePeriodicReport();
    });
  }
  
  /// Log a diagnostic event
  void _logEvent(String type, Map<String, dynamic> data) {
    final event = DiagnosticEvent(
      type: type,
      timestamp: DateTime.now(),
      data: Map<String, dynamic>.from(data),
    );
    
    _events.add(event);
    
    // Keep only the last 100 events to prevent memory issues
    if (_events.length > 100) {
      _events.removeAt(0);
    }
    
    if (_enableLogging) {
      debugPrint('🔍 $_tag: [$type] ${data.toString()}');
    }
  }
  
  /// Record initialization attempt
  void recordInitializationAttempt(bool success, {String? errorType, String? errorMessage}) {
    _totalInitializationAttempts++;
    if (success) {
      _successfulInitializations++;
    }
    
    _logEvent('InitializationAttempt', {
      'success': success,
      'error_type': errorType,
      'error_message': errorMessage,
      'attempt_number': _totalInitializationAttempts,
    });
    
    if (errorType != null) {
      _errorCounts[errorType] = (_errorCounts[errorType] ?? 0) + 1;
    }
  }
  
  /// Record detection session
  void recordDetectionSession(bool success, Duration duration, {String? errorType, String? errorMessage}) {
    _totalDetectionSessions++;
    if (success) {
      _successfulDetectionSessions++;
      _totalDetectionTime += duration;
    }
    
    _logEvent('DetectionSession', {
      'success': success,
      'duration_ms': duration.inMilliseconds,
      'error_type': errorType,
      'error_message': errorMessage,
      'session_number': _totalDetectionSessions,
    });
    
    if (errorType != null) {
      _errorCounts[errorType] = (_errorCounts[errorType] ?? 0) + 1;
    }
  }
  
  /// Record gesture detection result
  void recordGestureDetection(GestureType gesture, double confidence, String mode) {
    _logEvent('GestureDetected', {
      'gesture': gesture.toString(),
      'confidence': confidence,
      'mode': mode,
    });
  }
  
  /// Record MediaPipe availability check
  void recordAvailabilityCheck(Map<String, dynamic> result) {
    _logEvent('AvailabilityCheck', result);
  }
  
  /// Generate comprehensive diagnostic report
  Future<DiagnosticReport> generateReport() async {
    final report = DiagnosticReport();
    
    // Basic information
    report.timestamp = DateTime.now();
    report.sessionDuration = _sessionStartTime != null 
        ? DateTime.now().difference(_sessionStartTime!)
        : Duration.zero;
    
    // System information
    report.systemInfo = Map<String, dynamic>.from(_systemInfo);
    
    // Performance metrics
    report.performanceMetrics = {
      'total_initialization_attempts': _totalInitializationAttempts,
      'successful_initializations': _successfulInitializations,
      'initialization_success_rate': _totalInitializationAttempts > 0 
          ? _successfulInitializations / _totalInitializationAttempts 
          : 0.0,
      'total_detection_sessions': _totalDetectionSessions,
      'successful_detection_sessions': _successfulDetectionSessions,
      'detection_success_rate': _totalDetectionSessions > 0
          ? _successfulDetectionSessions / _totalDetectionSessions
          : 0.0,
      'total_detection_time_ms': _totalDetectionTime.inMilliseconds,
      'average_session_duration_ms': _successfulDetectionSessions > 0
          ? _totalDetectionTime.inMilliseconds / _successfulDetectionSessions
          : 0.0,
    };
    
    // Error analysis
    report.errorAnalysis = {
      'error_counts': Map<String, int>.from(_errorCounts),
      'most_common_errors': _getMostCommonErrors(),
      'error_patterns': _analyzeErrorPatterns(),
    };
    
    // Recent events
    report.recentEvents = _events.map((e) => e.toMap()).toList();
    
    // Recommendations
    report.recommendations = _generateRecommendations();
    
    return report;
  }
  
  /// Get most common errors
  List<Map<String, dynamic>> _getMostCommonErrors() {
    final sortedErrors = _errorCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedErrors.take(5).map((entry) => {
      'error_type': entry.key,
      'count': entry.value,
    }).toList();
  }
  
  /// Analyze error patterns
  Map<String, dynamic> _analyzeErrorPatterns() {
    final patterns = <String, dynamic>{};
    
    // Check for x86_64 issues
    bool hasX86Issues = _errorCounts.keys.any((error) => 
        error.contains('UnsatisfiedLinkError') || 
        error.contains('x86_64') ||
        error.contains('MediaPipeInitFailed'));
    
    if (hasX86Issues) {
      patterns['x86_64_architecture_issue'] = {
        'detected': true,
        'description': 'MediaPipe native library issues detected, likely x86_64 emulator',
        'solution': 'Switch to ARM64 emulator or use physical device',
      };
    }
    
    // Check for permission issues
    bool hasPermissionIssues = _errorCounts.keys.any((error) => 
        error.contains('Permission') || error.contains('Camera'));
    
    if (hasPermissionIssues) {
      patterns['permission_issue'] = {
        'detected': true,
        'description': 'Camera permission issues detected',
        'solution': 'Ensure camera permissions are granted',
      };
    }
    
    // Check for plugin integration issues
    bool hasPluginIssues = _errorCounts.keys.any((error) => 
        error.contains('MissingPluginException') || error.contains('Plugin'));
    
    if (hasPluginIssues) {
      patterns['plugin_integration_issue'] = {
        'detected': true,
        'description': 'Flutter plugin integration issues detected',
        'solution': 'Verify Android/iOS plugin registration',
      };
    }
    
    return patterns;
  }
  
  /// Generate recommendations based on diagnostic data
  List<String> _generateRecommendations() {
    final recommendations = <String>[];
    
    // Check initialization success rate
    if (_totalInitializationAttempts > 0) {
      final successRate = _successfulInitializations / _totalInitializationAttempts;
      if (successRate < 0.5) {
        recommendations.add('Low initialization success rate (${(successRate * 100).toStringAsFixed(1)}%) - check MediaPipe compatibility');
      }
    }
    
    // Check detection success rate
    if (_totalDetectionSessions > 0) {
      final detectionRate = _successfulDetectionSessions / _totalDetectionSessions;
      if (detectionRate < 0.8) {
        recommendations.add('Low detection success rate (${(detectionRate * 100).toStringAsFixed(1)}%) - consider fallback camera mode');
      }
    }
    
    // Platform-specific recommendations
    if (Platform.isAndroid && _errorCounts.containsKey('UnsatisfiedLinkError')) {
      recommendations.add('UnsatisfiedLinkError detected - likely x86_64 emulator. Use ARM64 emulator or physical device');
    }
    
    if (_errorCounts.containsKey('PermissionDenied')) {
      recommendations.add('Camera permission issues - ensure permissions are granted in app settings');
    }
    
    if (_errorCounts.containsKey('MissingPluginException')) {
      recommendations.add('Plugin integration issues - verify Flutter plugin registration');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('System appears to be functioning normally');
    }
    
    return recommendations;
  }
  
  /// Generate periodic report
  void _generatePeriodicReport() {
    generateReport().then((report) {
      if (_enableLogging) {
        debugPrint('🔍 $_tag: Periodic Report Generated');
        debugPrint('📊 Performance: ${report.performanceMetrics}');
        debugPrint('🚨 Errors: ${report.errorAnalysis}');
        debugPrint('💡 Recommendations: ${report.recommendations}');
      }
    }).catchError((e) {
      debugPrint('🔴 $_tag: Error generating periodic report: $e');
    });
  }
  
  /// Save diagnostic report to file
  Future<String?> saveReportToFile() async {
    try {
      final report = await generateReport();
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-');
      final fileName = 'gesture_diagnostics_$timestamp.json';
      final file = File('${directory.path}/$fileName');
      
      final jsonString = json.encode(report.toMap());
      await file.writeAsString(jsonString);
      
      debugPrint('🔍 $_tag: Diagnostic report saved to ${file.path}');
      return file.path;
    } catch (e) {
      debugPrint('🔴 $_tag: Error saving diagnostic report: $e');
      return null;
    }
  }
  
  /// Run comprehensive system diagnostics
  Future<Map<String, dynamic>> runSystemDiagnostics(ProHandGestureDetector detector) async {
    final diagnostics = <String, dynamic>{};
    
    try {
      // Basic detector information
      diagnostics['detector_initialized'] = detector.isInitialized;
      diagnostics['current_mode'] = detector.currentMode;
      diagnostics['current_gesture'] = detector.currentGesture.toString();
      diagnostics['current_confidence'] = detector.currentConfidence;
      
      // Generate diagnostic report
      final report = await generateReport();
      diagnostics['diagnostic_report'] = report.toMap();
      
      // Add basic system information
      diagnostics['platform'] = Platform.operatingSystem;
      diagnostics['is_android'] = Platform.isAndroid;
      diagnostics['is_ios'] = Platform.isIOS;
      
      return diagnostics;
    } catch (e) {
      diagnostics['error'] = 'Error running system diagnostics: $e';
      return diagnostics;
    }
  }
  
  /// Enable or disable logging
  void setLogging(bool enabled) {
    _enableLogging = enabled;
    _logEvent('LoggingStateChanged', {'enabled': enabled});
  }
  
  /// Clean up resources
  void dispose() {
    _periodicReportTimer?.cancel();
    _events.clear();
    _errorCounts.clear();
    debugPrint('🔍 $_tag: Diagnostics service disposed');
  }
}

/// Represents a diagnostic event
class DiagnosticEvent {
  final String type;
  final DateTime timestamp;
  final Map<String, dynamic> data;
  
  DiagnosticEvent({
    required this.type,
    required this.timestamp,
    required this.data,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'type': type,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
    };
  }
}

/// Comprehensive diagnostic report
class DiagnosticReport {
  late DateTime timestamp;
  late Duration sessionDuration;
  late Map<String, dynamic> systemInfo;
  late Map<String, dynamic> performanceMetrics;
  late Map<String, dynamic> errorAnalysis;
  late List<Map<String, dynamic>> recentEvents;
  late List<String> recommendations;
  
  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.toIso8601String(),
      'session_duration_ms': sessionDuration.inMilliseconds,
      'system_info': systemInfo,
      'performance_metrics': performanceMetrics,
      'error_analysis': errorAnalysis,
      'recent_events': recentEvents,
      'recommendations': recommendations,
    };
  }
  
  /// Generate human-readable report
  String generateHumanReadableReport() {
    final buffer = StringBuffer();
    buffer.writeln('=== MediaPipe Gesture Detection Diagnostic Report ===');
    buffer.writeln('Generated: ${timestamp.toLocal()}');
    buffer.writeln('Session Duration: ${_formatDuration(sessionDuration)}');
    buffer.writeln('');
    
    buffer.writeln('SYSTEM INFORMATION:');
    systemInfo.forEach((key, value) {
      buffer.writeln('  $key: $value');
    });
    buffer.writeln('');
    
    buffer.writeln('PERFORMANCE METRICS:');
    performanceMetrics.forEach((key, value) {
      if (value is double) {
        buffer.writeln('  $key: ${value.toStringAsFixed(2)}');
      } else {
        buffer.writeln('  $key: $value');
      }
    });
    buffer.writeln('');
    
    buffer.writeln('ERROR ANALYSIS:');
    final errorCounts = Map<String, dynamic>.from(errorAnalysis['error_counts'] ?? {});
    if (errorCounts.isEmpty) {
      buffer.writeln('  No errors recorded');
    } else {
      errorCounts.forEach((error, count) {
        buffer.writeln('  $error: $count occurrences');
      });
    }
    buffer.writeln('');
    
    buffer.writeln('RECOMMENDATIONS:');
    for (final recommendation in recommendations) {
      buffer.writeln('  • $recommendation');
    }
    
    return buffer.toString();
  }
  
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
}