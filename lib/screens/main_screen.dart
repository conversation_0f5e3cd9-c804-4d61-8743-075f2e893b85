import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/game_layout.dart';
import '../providers/game_state_provider.dart';
import '../providers/settings_provider.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  @override
  void initState() {
    super.initState();
    // Load settings in SettingsProvider for UI synchronization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSettingsForUI();
    });
  }

  Future<void> _loadSettingsForUI() async {
    final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    
    // Load settings into SettingsProvider for UI display
    // GameStateProvider loads its own settings automatically on startup
    await settingsProvider.loadSettings();
    debugPrint('🎨 UI: SettingsProvider settings loaded for UI display');
  }

  void _openSettings() {
    Navigator.pushNamed(context, '/settings');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GameLayout(
        onSettingsPressed: _openSettings,
      ),
    );
  }
}