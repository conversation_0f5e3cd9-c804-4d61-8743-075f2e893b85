import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/settings_menu.dart';
import '../providers/settings_provider.dart';
import '../providers/game_state_provider.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => _closeSettings(context),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/blurred_bg.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: SettingsMenu(
          onReturnToGame: () => _closeSettings(context),
        ),
      ),
    );
  }

  void _closeSettings(BuildContext context) {
    // Settings are automatically applied and saved via the "Return to Game" button
    // No need to duplicate the save process here
    debugPrint('⚙️ CLOSE: Closing settings screen');
    Navigator.pop(context);
  }
}