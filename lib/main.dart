import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'screens/welcome_screen.dart';
import 'screens/main_screen.dart';
import 'screens/settings_screen.dart';
import 'providers/game_state_provider.dart';
import 'providers/settings_provider.dart';
import 'package:permission_handler/permission_handler.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();
  
  // Request camera permission
  final hasPermission = await _requestCameraPermission();
  
  // Set preferred orientations
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  runApp(RockPaperScissorsApp(hasCameraPermission: hasPermission));
}

// Request camera permission with proper error handling
Future<bool> _requestCameraPermission() async {
  try {
    final status = await Permission.camera.request();
    if (status.isGranted) {
      debugPrint('🎮 CAMERA: Camera permission granted');
      return true;
    } else if (status.isPermanentlyDenied) {
      debugPrint('🔴 CAMERA ERROR: Camera permission permanently denied');
      return false;
    } else {
      debugPrint('🔴 CAMERA ERROR: Camera permission denied');
      return false;
    }
  } catch (e) {
    debugPrint('🔴 CAMERA ERROR: Error requesting camera permission: $e');
    return false;
  }
}

class RockPaperScissorsApp extends StatelessWidget {
  final bool hasCameraPermission;
  
  const RockPaperScissorsApp({super.key, required this.hasCameraPermission});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => GameStateProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ],
      child: MaterialApp(
        title: 'Rock Paper Scissors',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
          fontFamily: 'Genos',
        ),
        home: hasCameraPermission 
          ? const WelcomeScreen()  // Back to normal welcome screen
          : const NoCameraPermissionScreen(),
        routes: {
          '/welcome': (context) => const WelcomeScreen(),
          '/main': (context) => const MainScreen(),
          '/settings': (context) => const SettingsScreen(),
        },
      ),
    );
  }
}

class NoCameraPermissionScreen extends StatelessWidget {
  const NoCameraPermissionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.no_photography, // Using a valid icon instead of camera_off
                size: 80,
                color: Colors.red,
              ),
              const SizedBox(height: 20),
              const Text(
                'Camera Permission Required',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                'This app needs camera access to detect hand gestures. Please enable camera permission in settings.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 30),
              ElevatedButton(
                onPressed: () async {
                  // Open app settings
                  await openAppSettings();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
                child: const Text(
                  'Activate Camera',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}