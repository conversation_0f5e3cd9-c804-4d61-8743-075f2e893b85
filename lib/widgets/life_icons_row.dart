import 'package:flutter/material.dart';


class LifeIconsRow extends StatelessWidget {
  final int lives;
  final bool isPlayer;

  const LifeIconsRow({
    super.key,
    required this.lives,
    required this.isPlayer,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(lives, (index) => _buildLifeIcon()),
    );
  }

  Widget _buildLifeIcon() {
    if (isPlayer) {
      // Player uses life icons
      return Padding(
        padding: const EdgeInsets.only(right: 2),
        child: Image.asset(
          'assets/icons/life-icon.png',
          width: 20,
          height: 20,
        ),
      );
    } else {
      // Program uses battery icons based on remaining lives
      String batteryIcon;
      if (lives == 1) {
        batteryIcon = 'assets/icons/baterie-faible.png';
      } else if (lives <= 3) {
        batteryIcon = 'assets/icons/baterie-demi.png';
      } else {
        batteryIcon = 'assets/icons/baterie-plein.png';
      }
      
      return Padding(
        padding: const EdgeInsets.only(right: 2),
        child: Image.asset(
          batteryIcon,
          width: 20,
          height: 20,
        ),
      );
    }
  }
}