import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_state_provider.dart';

class JackpotDisplay extends StatelessWidget {
  const JackpotDisplay({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<GameStateProvider>(
      builder: (context, gameState, child) {
        bool hasPlayerJackpot = gameState.playerJackpotPoints > 0;
        bool hasProgramJackpot = gameState.programJackpotPoints > 0;
        bool hasAnyJackpot = hasPlayerJackpot || hasProgramJackpot;

        return Container(
          width: 120,
          height: 80,
          child: Stack(
            children: [
              // Background image
              Opacity(
                opacity: hasAnyJackpot ? 0.0 : 1.0,
                child: Image.asset(
                  'assets/images/no_jackpot_yet.png',
                  width: 120,
                  height: 80,
                  fit: BoxFit.contain,
                ),
              ),
              
              // Jackpot points display
              if (hasAnyJackpot) ...[
                // Player jackpot points (blue, top)
                if (hasPlayerJackpot)
                  Positioned(
                    top: 10,
                    right: 10,
                    child: Text(
                      '${gameState.playerJackpotPoints}',
                      style: const TextStyle(
                        color: Colors.blue,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Genos',
                      ),
                    ),
                  ),
                
                // Program jackpot points (red, bottom)
                if (hasProgramJackpot)
                  Positioned(
                    bottom: 10,
                    right: 10,
                    child: Text(
                      '${gameState.programJackpotPoints}',
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Genos',
                      ),
                    ),
                  ),
              ],
            ],
          ),
        );
      },
    );
  }
}