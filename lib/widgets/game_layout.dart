import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:camera/camera.dart';
import 'dart:async';
import 'dart:math' as math;
import '../providers/game_state_provider.dart';
import '../services/gesture_detector.dart' as gesture_service;
import '../widgets/life_icons_row.dart';
import '../widgets/score_display.dart';
import '../widgets/jackpot_display.dart';
import '../widgets/game_timer.dart';
import '../widgets/player_info.dart';


import '../widgets/player_table_dialog.dart';


class GameLayout extends StatefulWidget {
 final VoidCallback onSettingsPressed;


 const GameLayout({
   super.key,
   required this.onSettingsPressed,
 });


 @override
 State<GameLayout> createState() => _GameLayoutState();
}


class _GameLayoutState extends State<GameLayout> with TickerProviderStateMixin {
 late gesture_service.HandGestureDetector gestureDetector;
 bool _isDetecting = false;
 bool _gameStarted = false;
 bool _cameraError = false;
 String _cameraErrorMessage = '';
 StreamSubscription<gesture_service.GestureType>? _gestureSubscription;
  // Animation controllers
 late AnimationController _fadeController;
 late AnimationController _scaleController;
  // Game state
 final List<String> _countdownImages = ['3.png', '2.png', '1.png'];
 final List<String> _choiceImages = ['ciseaux.png', 'pierre.png', 'papier.png'];
  int _currentCountdownIndex = 0;
 bool _showingCountdown = false;
 bool _showingResult = false;
 String? _currentResultImage;
 String? _currentChoiceImage;
 String? _detectedGesture;


 @override
 void initState() {
   super.initState();
  
   // Initialize animation controllers
   _fadeController = AnimationController(
     duration: const Duration(milliseconds: 500),
     vsync: this,
   );
  
   _scaleController = AnimationController(
     duration: const Duration(milliseconds: 300),
     vsync: this,
   );
  
   // Initialize gesture detector with error handling
   gestureDetector = gesture_service.HandGestureDetector();
  
   // Defer complex initialization to prevent main thread blocking
   WidgetsBinding.instance.addPostFrameCallback((_) {
     _initializeWithErrorHandling();
   });
 }
  Future<void> _initializeWithErrorHandling() async {
   try {
     // Initialize gesture detector asynchronously
     await _initializeGestureDetector();
    
     // Load players data with error handling
     await _loadPlayersDataSafely();
   } catch (e) {
     debugPrint('🔴 GAME ERROR: Error during game initialization: $e');
     // Show fallback UI if needed
     _showErrorSnackBar('Initialization completed with some limitations');
   }
 }
  Future<void> _loadPlayersDataSafely() async {
   try {
     Provider.of<GameStateProvider>(context, listen: false).loadPlayersData();
   } catch (e) {
     debugPrint('🔴 GAME ERROR: Error loading players data: $e');
     // Continue without player data - app should not crash
   }
 }


 Future<void> _initializeGestureDetector() async {
   try {
     final success = await gestureDetector.initialize();
     if (!success) {
       debugPrint('🔴 CAMERA ERROR: Gesture detector initialization failed');
       setState(() {
         _cameraError = true;
         _cameraErrorMessage = 'Camera initialization failed. Please check permissions.';
       });
       _showErrorSnackBar('Camera initialization failed. Using simulation mode.');
     } else {
       debugPrint('🎮 CAMERA: Gesture detector initialized successfully');
       // Listen to gesture stream if available
       _setupGestureListener();
     }
   } catch (e) {
     debugPrint('🔴 CAMERA ERROR: Error during gesture detector initialization: $e');
     setState(() {
       _cameraError = true;
       _cameraErrorMessage = 'Camera error: $e';
     });
     _showErrorSnackBar('Camera error. Using simulation mode.');
   }
 }
  // Set up gesture listener for real-time detection
 void _setupGestureListener() {
   try {
     _gestureSubscription?.cancel();
    
     debugPrint('🎮 GAME: Setting up gesture listener');
    
     // Try to use the gesture stream if available
     if (gestureDetector.gestureStream != null) {
       debugPrint('🎮 GAME: Using gesture stream');
       _gestureSubscription = gestureDetector.gestureStream!.listen(
         (gesture) {
           debugPrint('🎮 GAME: Stream received gesture: $gesture');
           if (gesture != gesture_service.GestureType.none &&
               gesture != gesture_service.GestureType.unknown) {
             _onGestureDetected(gesture);
           }
         },
         onError: (error) {
           debugPrint('🔴 GAME: Gesture stream error: $error');
           // Fall back to polling on stream error
           _startPollingFallback();
         }
       );
     } else {
       debugPrint('🎮 GAME: Gesture stream not available, using polling fallback');
       _startPollingFallback();
     }
    
     // Always set up the polling mechanism as a secondary fallback
     // This ensures detection works even if the stream has issues
     if (mounted && _isDetecting) {
       Timer(const Duration(milliseconds: 1000), () {
         if (mounted && _isDetecting) {
           _detectGesture();
         }
       });
     }
   } catch (e) {
     debugPrint('🔴 GESTURE DETECTION: Error setting up gesture listener: $e');
     // Fall back to polling if stream setup fails
     _startPollingFallback();
   }
 }
  // Start real gesture detection polling (no simulation)
 void _startPollingFallback() {
   debugPrint('🎮 GAME: Starting real gesture polling (no simulation)');
   Timer.periodic(const Duration(milliseconds: 300), (timer) {
     if (!_isDetecting || !mounted) {
       timer.cancel();
       return;
     }
    
     try {
       // Only check for actual gesture detection, no simulation
       String playerGesture = gestureDetector.getGesture();
       if (['papier', 'pierre', 'ciseaux'].contains(playerGesture)) {
         debugPrint('🎮 GAME: Real gesture detected: $playerGesture');
         timer.cancel();
         _processValidGesture(playerGesture);
       }
     } catch (e) {
       debugPrint('🔴 GAME: Error checking gesture: $e');
     }
   });
 }
  // Handle gesture detected from stream
 void _onGestureDetected(gesture_service.GestureType gesture) {
   String gestureString;
   switch (gesture) {
     case gesture_service.GestureType.rock:
       gestureString = 'pierre';
       break;
     case gesture_service.GestureType.paper:
       gestureString = 'papier';
       break;
     case gesture_service.GestureType.scissors:
       gestureString = 'ciseaux';
       break;
     default:
       return; // Ignore invalid gestures
   }
  
   debugPrint('🎮 GAME: Processing stream gesture: $gestureString');
   _processValidGesture(gestureString);
 }
  // Handle detected gesture
 void _handleDetectedGesture(gesture_service.GestureType gesture) {
   if (!_isDetecting) return;
  
   debugPrint('🎮 GAME: Handling detected gesture: $gesture');
  
   String? newDetectedGesture;
  
   // Use if-else instead of switch to avoid constant context issues
   if (gesture == gesture_service.GestureType.rock) {
     newDetectedGesture = "pierre";
   } else if (gesture == gesture_service.GestureType.paper) {
     newDetectedGesture = "papier";
   } else if (gesture == gesture_service.GestureType.scissors) {
     newDetectedGesture = "ciseaux";
   } else if (gesture == gesture_service.GestureType.none ||
              gesture == gesture_service.GestureType.unknown) {
     newDetectedGesture = null;
   }
  
   debugPrint('🎮 GAME: Mapped gesture to: $newDetectedGesture');
  
   // Only update state if the gesture has changed
   if (_detectedGesture != newDetectedGesture) {
     setState(() {
       _detectedGesture = newDetectedGesture;
     });
     debugPrint('🎮 GAME: Updated _detectedGesture to: $_detectedGesture');
   }
  
   // If we have a valid gesture, process it
   if (newDetectedGesture != null &&
       ['papier', 'pierre', 'ciseaux'].contains(newDetectedGesture)) {
     debugPrint('🎮 GAME: Valid gesture detected, processing: $newDetectedGesture');
     _processValidGesture(newDetectedGesture);
   }
 }


 @override
 void dispose() {
   _fadeController.dispose();
   _scaleController.dispose();
   _gestureSubscription?.cancel();
   gestureDetector.dispose();
   super.dispose();
 }


 // UI Event Handlers
 void _onStartGamePressed() {
   if (!_gameStarted) {
     _startGame();
   }
 }


 void _onPlayerButtonPressed() {
   _showPlayerTable();
 }


 // Game Logic
 void _startGame() async {
   final gameState = Provider.of<GameStateProvider>(context, listen: false);
  
   setState(() {
     _gameStarted = true;
   });
  
   // Start timer
   gameState.startTimer();
  
   // Play countdown
   _playCountdown();
 }


 void _playCountdown() async {
   setState(() {
     _showingCountdown = true;
     _currentCountdownIndex = 0;
   });


   // IMPORTANT: Démarrer la caméra EN ARRIÈRE-PLAN avant le countdown
   // pour qu'elle soit prête à détecter le geste du joueur
   debugPrint('🎮 GAME: Starting camera in background before countdown');
   await _startGestureDetection();


   // Play countdown sound
   final gameState = Provider.of<GameStateProvider>(context, listen: false);
   await gameState.playEffect('audio/3_2_1_kids.mp3');


   _showCountdownSequence();
 }


 void _showCountdownSequence() {
   if (_currentCountdownIndex < _countdownImages.length) {
     setState(() {
       // Current countdown image will be displayed by the UI
     });
    
     // Schedule next countdown image
     Future.delayed(const Duration(seconds: 1), () {
       if (mounted) {
         setState(() {
           _currentCountdownIndex++;
         });
         _showCountdownSequence();
       }
     });
   } else {
     // Countdown finished, la caméra est déjà active en arrière-plan
     setState(() {
       _showingCountdown = false;
     });
     debugPrint('🎮 GAME: Countdown finished, camera already detecting gestures');
   }
 }






 Future<void> _startGestureDetection() async {
   debugPrint('🎮 GAME: Starting gesture detection in GAME mode');


   try {
     // Vérifier d'abord si le détecteur est initialisé
     if (!gestureDetector.isInitialized) {
       debugPrint('🎮 GAME: Detector not initialized, initializing...');
       final initialized = await gestureDetector.initialize();
       if (!initialized) {
         debugPrint('🔴 GAME: Failed to initialize detector');
         _showErrorSnackBar('Impossible d\'initialiser la détection de gestes');
         _playNextRound(); // Passer au tour suivant au lieu de bloquer
         return;
       }
     }
    
     // Start detection in GAME mode for faster response
     await gestureDetector.startDetection(mode: "GAME");
     setState(() {
       _isDetecting = true;
       _detectedGesture = null;
     });


     // Always set up gesture listener, regardless of whether we have a stream
     // The listener will handle both native stream and fallback cases
     _setupGestureListener();


     debugPrint('🎮 GAME: Gesture detection started successfully');
    
     // Ajouter un timeout pour éviter un blocage infini
     Timer(const Duration(seconds: 15), () {
       if (mounted && _isDetecting && _detectedGesture == null) {
         debugPrint('⏰ GAME: Gesture detection timeout, forcing next round');
         _processValidGesture('pierre'); // Utiliser un geste par défaut
       }
     });
   } catch (e) {
     debugPrint('🔴 GAME: Error starting gesture detection: $e');
     // Ne pas continuer avec fallback si detection failed, passer au tour suivant
     _showErrorSnackBar('Erreur de détection de gestes');
     _playNextRound();
   }
 }






















 // Real gesture detection method (no simulation)
 void _detectGesture() async {
   if (!_isDetecting) return;
  
   String playerGesture = gestureDetector.getGesture();
   debugPrint('🎮 GAME REAL: Checking for real gesture: "$playerGesture"');
  
   if (!['papier', 'pierre', 'ciseaux'].contains(playerGesture)) {
     // Wait and try again based on difficulty
     final gameState = Provider.of<GameStateProvider>(context, listen: false);
     double delay = _getDetectionDelay(gameState.difficulty);
    
     debugPrint('🎮 GAME REAL: No valid gesture detected, retrying in ${delay}s');
    
     Future.delayed(Duration(milliseconds: (delay * 1000).toInt()), () {
       if (mounted && _isDetecting) {
         _detectGesture();
       }
     });
   } else {
     // Valid gesture detected
     debugPrint('🎮 GAME REAL: Valid real gesture detected: "$playerGesture"');
     _processValidGesture(playerGesture);
   }
 }
  // Process a valid gesture
 void _processValidGesture(String playerGesture) async {
   // Only process if we haven't already processed a gesture
   if (!_isDetecting) return;
  
   debugPrint('🎮 GAME: Processing valid gesture: $playerGesture');
  
   await gestureDetector.stopDetection();
   setState(() {
     _isDetecting = false;
   });
   _displayProgramChoice(playerGesture);
 }


 double _getDetectionDelay(String difficulty) {
   switch (difficulty) {
     case 'hard':
       return 0.0;
     case 'medium':
       return 0.5;
     case 'light':
       return 1.0;
     default:
       return 0.5;
   }
 }


 void _displayProgramChoice(String playerChoice) {
   // Random program choice
   final programChoiceImage = _choiceImages[math.Random().nextInt(_choiceImages.length)];
  
   setState(() {
     _currentChoiceImage = programChoiceImage;
   });
  
   // Show program choice for 1.5 seconds then evaluate
   Future.delayed(const Duration(milliseconds: 1500), () {
     if (mounted) {
       _evaluateResult(playerChoice, programChoiceImage);
     }
   });
 }


 void _evaluateResult(String playerChoice, String programChoiceImage) {
   final gameState = Provider.of<GameStateProvider>(context, listen: false);
  
   // Extract program choice name from image path
   String programChoice = programChoiceImage.split('.')[0];
  
   // DEBUG: Log the choices for debugging
   debugPrint('🎮 GAME EVALUATION:');
   debugPrint('🎮 Player choice: $playerChoice');
   debugPrint('🎮 Program choice image: $programChoiceImage');
   debugPrint('🎮 Program choice extracted: $programChoice');
  
   String resultImage = 'egal.png';
   String soundFile = 'equal.mp3';
   bool playerWon = false;
  
   if (playerChoice == programChoice) {
     // Tie
     resultImage = 'egal.png';
     soundFile = 'audio/equal.mp3';
     debugPrint('🟡 GAME RESULT: TIE (both chose $playerChoice)');
   } else if ((playerChoice == 'papier' && programChoice == 'pierre') ||
              (playerChoice == 'pierre' && programChoice == 'ciseaux') ||
              (playerChoice == 'ciseaux' && programChoice == 'papier')) {
     // Player wins
     resultImage = 'you_win.png';
     soundFile = 'audio/you_win.mp3';
     playerWon = true;
     debugPrint('🟢 GAME RESULT: PLAYER WINS! ($playerChoice beats $programChoice)');
     gameState.updateScore(playerWon: true);
     gameState.updateLives(playerLostLife: false); // Program loses a life
   } else {
     // Player loses
     resultImage = 'you_lose.png';
     soundFile = 'audio/you_lose.mp3';
     playerWon = false;
     debugPrint('🔴 GAME RESULT: PLAYER LOSES! ($programChoice beats $playerChoice)');
     gameState.updateScore(playerWon: false);
     gameState.updateLives(playerLostLife: true); // Player loses a life
   }
  
   // Additional debugging
   debugPrint('🎮 Result image: $resultImage');
   debugPrint('🎮 Sound file: $soundFile');
   debugPrint('🎮 Player score: ${gameState.scorePlayer}');
   debugPrint('🎮 Program score: ${gameState.scoreProgram}');
   debugPrint('🎮 Player lives: ${gameState.playerLives}');
   debugPrint('🎮 Program lives: ${gameState.programLives}');
  
   // Play result sound
   gameState.playEffect(soundFile);
  
   // Show result
   _showResult(resultImage);
  
   // Check game over condition
   if (gameState.playerLives <= 0 || gameState.programLives <= 0) {
     String finalResult = gameState.playerLives > 0 ? 'Victory' : 'Defeat';
     gameState.saveGameResult(
       finalResult: finalResult,
       playerPoints: gameState.scorePlayer,
       programPoints: gameState.scoreProgram,
       playerJackpot: gameState.playerJackpotPoints,
       programJackpot: gameState.programJackpotPoints,
     );
    
     Future.delayed(const Duration(milliseconds: 1500), () {
       if (mounted) {
         _displayGameOver();
       }
     });
   } else {
     // Continue to next round
     Future.delayed(const Duration(milliseconds: 1500), () {
       if (mounted) {
         _playNextRound();
       }
     });
   }
 }


 void _showResult(String resultImage) {
   setState(() {
     _showingResult = true;
     _currentResultImage = resultImage;
     _currentChoiceImage = null; // Clear choice image
   });
  
   // Hide result after 1.5 seconds
   Future.delayed(const Duration(milliseconds: 1500), () {
     if (mounted) {
       setState(() {
         _showingResult = false;
         _currentResultImage = null;
       });
     }
   });
 }


 void _playNextRound() async {
   final gameState = Provider.of<GameStateProvider>(context, listen: false);
  
   // Play countdown sound
   await gameState.playEffect('3_2_go/${gameState.sineshifterSound}');
  
   // Restart detection after delay
   Future.delayed(const Duration(seconds: 1), () {
     if (mounted) {
       _startGestureDetection();
     }
   });
 }


 void _displayGameOver() {
   final gameState = Provider.of<GameStateProvider>(context, listen: false);
   gameState.stopTimer();
  
   // Play game over sound
   gameState.playEffect('audio/gameover.mp3');
  
   showDialog(
     context: context,
     barrierDismissible: false,
     builder: (context) => _GameOverDialog(
       onRestart: _restartGame,
       onQuit: _quitGame,
     ),
   );
 }


 void _restartGame() {
   final gameState = Provider.of<GameStateProvider>(context, listen: false);
   gameState.restartGame();
  
   setState(() {
     _gameStarted = false;
     _isDetecting = false;
     _showingCountdown = false;
     _showingResult = false;
     _currentResultImage = null;
     _currentChoiceImage = null;
     _currentCountdownIndex = 0;
     _detectedGesture = null;
   });
  
   Navigator.pop(context); // Close game over dialog
 }


 void _quitGame() {
   // In a real app, you might want to go back to main menu
   // For now, we'll just restart
   _restartGame();
 }


 void _showPlayerTable() {
   showDialog(
     context: context,
     builder: (context) => const PlayerTableDialog(),
   );
 }


 void _showErrorSnackBar(String message) {
   ScaffoldMessenger.of(context).showSnackBar(
     SnackBar(
       content: Text(message),
       backgroundColor: Colors.red,
     ),
   );
 }


 @override
 Widget build(BuildContext context) {
   return Consumer<GameStateProvider>(
     builder: (context, gameState, child) {
       return Container(
         decoration: BoxDecoration(
           image: DecorationImage(
             image: AssetImage(gameState.bgImage),
             fit: BoxFit.cover,
           ),
         ),
         child: Scaffold(
           backgroundColor: Colors.transparent,
           body: SafeArea(
             child: Stack(
               children: [
                 // Main game content
                 _buildGameContent(gameState),
                
                 // Overlay content (countdown, choices, results)
                 _buildOverlayContent(),
                
                 // UI Controls
                 _buildUIControls(gameState),
               ],
             ),
           ),
         ),
       );
     },
   );
 }


 Widget _buildGameContent(GameStateProvider gameState) {
   return Column(
     children: [
       // Top section: Timer, Lives, Score, Jackpot
       _buildTopSection(gameState),
      
       // Middle section: Main game area
       const Expanded(child: SizedBox()),
      
       // Bottom section: Player info and controls
       _buildBottomSection(gameState),
     ],
   );
 }


 Widget _buildTopSection(GameStateProvider gameState) {
   return Padding(
     padding: const EdgeInsets.all(16.0),
     child: Column(
       children: [
         // Timer at top center
         GameTimer(),
        
         const SizedBox(height: 10),
        
         Row(
           mainAxisAlignment: MainAxisAlignment.spaceBetween,
           children: [
             // Lives on left
             Column(
               crossAxisAlignment: CrossAxisAlignment.start,
               children: [
                 LifeIconsRow(
                   lives: gameState.playerLives,
                   isPlayer: true,
                 ),
                 const SizedBox(height: 5),
                 LifeIconsRow(
                   lives: gameState.programLives,
                   isPlayer: false,
                 ),
               ],
             ),
            
             // Score and Jackpot on right
             Column(
               crossAxisAlignment: CrossAxisAlignment.end,
               children: [
                 ScoreDisplay(),
                 const SizedBox(height: 5),
                 JackpotDisplay(),
               ],
             ),
           ],
         ),
       ],
     ),
   );
 }


 Widget _buildBottomSection(GameStateProvider gameState) {
   return Padding(
     padding: const EdgeInsets.all(16.0),
     child: Row(
       mainAxisAlignment: MainAxisAlignment.spaceBetween,
       children: [
         // Settings button
         IconButton(
           onPressed: widget.onSettingsPressed,
           icon: Image.asset(
             'assets/icons/settings-icon.png',
             width: 30,
             height: 30,
           ),
         ),
        
         // Player info
         PlayerInfo(),
        
         // Player button 
         IconButton(
           onPressed: _onPlayerButtonPressed,
           icon: Image.asset(
             'assets/icons/player.png',
             width: 30,
             height: 30,
           ),
         ),
       ],
     ),
   );
 }


 Widget _buildOverlayContent() {
   return Center(
     child: AnimatedSwitcher(
       duration: const Duration(milliseconds: 300),
       child: _buildCurrentOverlay(),
     ),
   );
 }


 Widget _buildCurrentOverlay() {
   if (!_gameStarted) {
     return GestureDetector(
       onTap: _onStartGamePressed,
       child: Image.asset(
         'assets/images/start_1.png',
         width: MediaQuery.of(context).size.width * 0.6,
         height: MediaQuery.of(context).size.width * 0.6,
         key: const ValueKey('start_button'),
       ),
     );
   }
  
   if (_showingCountdown && _currentCountdownIndex < _countdownImages.length) {
     return Image.asset(
       'assets/images/${_countdownImages[_currentCountdownIndex]}',
       width: MediaQuery.of(context).size.width * 0.6,
       height: MediaQuery.of(context).size.width * 0.6,
       key: ValueKey('countdown_$_currentCountdownIndex'),
     );
   }
  
   if (_currentChoiceImage != null) {
     return Image.asset(
       'assets/images/$_currentChoiceImage',
       width: MediaQuery.of(context).size.width * 0.6,
       height: MediaQuery.of(context).size.width * 0.6,
       key: ValueKey('choice_$_currentChoiceImage'),
     );
   }
  
   if (_showingResult && _currentResultImage != null) {
     return Image.asset(
       'assets/images/$_currentResultImage',
       width: MediaQuery.of(context).size.width * 0.6,
       height: MediaQuery.of(context).size.width * 0.6,
       key: ValueKey('result_$_currentResultImage'),
     );
   }
  
   if (_isDetecting) {
     if (_cameraError) {
       return _buildCameraErrorDisplay();
     }
     // Pas de preview nécessaire - la caméra détecte en arrière-plan
     return _buildDetectionIndicator();
   }
  
   return const SizedBox.shrink();
 }


 Widget _buildCameraPreview() {
   final cameraController = gestureDetector.cameraController;


   return Stack(
     children: [
       // Camera preview ou indicateur de chargement
       Container(
         width: MediaQuery.of(context).size.width * 0.8,
         height: MediaQuery.of(context).size.width * 0.8,
         decoration: BoxDecoration(
           borderRadius: BorderRadius.circular(20),
           border: Border.all(color: Colors.white, width: 3),
           color: cameraController?.value.isInitialized == true ? null : Colors.black54,
         ),
         child: ClipRRect(
           borderRadius: BorderRadius.circular(17),
           child: cameraController?.value.isInitialized == true
               ? CameraPreview(cameraController!)
               : const Center(
                   child: CircularProgressIndicator(color: Colors.white),
                 ),
         ),
       ),


       // Informations de détection en overlay
       Positioned(
         top: 8,
         left: 8,
         right: 8,
         child: _buildDetectionInfo(),
       ),
     ],
   );
 }


 Widget _buildDetectionIndicator() {
   return Container(
     width: MediaQuery.of(context).size.width * 0.6,
     height: MediaQuery.of(context).size.width * 0.6,
     decoration: BoxDecoration(
       borderRadius: BorderRadius.circular(20),
       border: Border.all(color: Colors.white, width: 3),
       color: Colors.black.withOpacity(0.3),
     ),
     child: Column(
       mainAxisAlignment: MainAxisAlignment.center,
       children: [
         Icon(
           Icons.camera_alt,
           size: 48,
           color: Colors.white.withOpacity(0.8),
         ),
         const SizedBox(height: 16),
         Text(
           'Caméra active\nPrêt à détecter',
           textAlign: TextAlign.center,
           style: TextStyle(
             color: Colors.white.withOpacity(0.8),
             fontSize: 16,
             fontWeight: FontWeight.w500,
           ),
         ),
         const SizedBox(height: 16),
         _buildDetectionInfo(),
       ],
     ),
   );
 }


 Widget _buildDetectionInfo() {
   return Container(
     padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
     decoration: BoxDecoration(
       color: Colors.black.withOpacity(0.7),
       borderRadius: BorderRadius.circular(8),
     ),
     child: Column(
       crossAxisAlignment: CrossAxisAlignment.start,
       mainAxisSize: MainAxisSize.min,
       children: [
         Row(
           children: [
             Icon(
               gestureDetector.detectedHand == "Left" ? Icons.back_hand :
               gestureDetector.detectedHand == "Right" ? Icons.front_hand :
               Icons.help_outline,
               color: Colors.white,
               size: 16,
             ),
             const SizedBox(width: 4),
             Text(
               'Main: ${gestureDetector.detectedHand}',
               style: const TextStyle(
                 color: Colors.white,
                 fontSize: 12,
                 fontFamily: 'Genos',
               ),
             ),
           ],
         ),
         if (gestureDetector.currentGesture != gesture_service.GestureType.none) ...[
           const SizedBox(height: 2),
           Text(
             'Geste: ${gestureDetector.currentGesture.name} (${(gestureDetector.currentConfidence * 100).toStringAsFixed(1)}%)',
             style: const TextStyle(
               color: Colors.white,
               fontSize: 12,
               fontFamily: 'Genos',
             ),
           ),
         ],
       ],
     ),
   );
 }


 Widget _buildCameraErrorDisplay() {
   return Container(
     width: MediaQuery.of(context).size.width * 0.8,
     height: MediaQuery.of(context).size.width * 0.8,
     decoration: BoxDecoration(
       borderRadius: BorderRadius.circular(20),
       border: Border.all(color: Colors.red, width: 3),
       color: Colors.black54,
     ),
     child: Center(
       child: Column(
         mainAxisAlignment: MainAxisAlignment.center,
         children: [
           const Icon(
             Icons.error_outline,
             color: Colors.red,
             size: 50,
           ),
           const SizedBox(height: 10),
           const Text(
             'Camera Error',
             style: TextStyle(
               color: Colors.white,
               fontSize: 20,
               fontWeight: FontWeight.bold,
             ),
           ),
           const SizedBox(height: 10),
           Text(
             _cameraErrorMessage,
             textAlign: TextAlign.center,
             style: const TextStyle(
               color: Colors.white70,
               fontSize: 14,
             ),
           ),
           const SizedBox(height: 20),
           ElevatedButton(
             onPressed: () {
               // Try to reinitialize camera
               _initializeGestureDetector();
             },
             child: const Text('Retry Camera'),
           ),
         ],
       ),
     ),
   );
 }


 Widget _buildUIControls(GameStateProvider gameState) {
   return const SizedBox.shrink(); // Placeholder for additional controls if needed
 }
}


// Game Over Dialog Widget
class _GameOverDialog extends StatelessWidget {
 final VoidCallback onRestart;
 final VoidCallback onQuit;


 const _GameOverDialog({
   required this.onRestart,
   required this.onQuit,
 });


 @override
 Widget build(BuildContext context) {
   return Dialog(
     backgroundColor: Colors.transparent,
     child: Container(
       padding: const EdgeInsets.all(20),
       decoration: BoxDecoration(
         image: const DecorationImage(
           image: AssetImage('assets/images/blurred_bg.png'),
           fit: BoxFit.cover,
         ),
         borderRadius: BorderRadius.circular(20),
       ),
       child: Column(
         mainAxisSize: MainAxisSize.min,
         children: [
           // Game Over Image
           Image.asset(
             'assets/images/gameover.png',
             width: 200,
             height: 150,
           ),
          
           const SizedBox(height: 20),
          
           // Buttons
           Row(
             mainAxisAlignment: MainAxisAlignment.spaceEvenly,
             children: [
               // Quit Button
               GestureDetector(
                 onTap: onQuit,
                 child: Image.asset(
                   'assets/images/quit.png',
                   width: 80,
                   height: 80,
                 ),
               ),
              
               // Play Again Button
               GestureDetector(
                 onTap: onRestart,
                 child: Image.asset(
                   'assets/images/play.png',
                   width: 80,
                   height: 80,
                 ),
               ),
             ],
           ),
         ],
       ),
     ),
   );
 }
}



