import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_state_provider.dart';

class PlayerResultsDialog extends StatelessWidget {
  final String playerName;

  const PlayerResultsDialog({
    super.key,
    required this.playerName,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Consumer<GameStateProvider>(
        builder: (context, gameState, child) {
          final results = List.from(gameState.playersData['players'][playerName] ?? []);
          
          return Container(
            width: MediaQuery.of(context).size.width * 0.9,
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              image: const DecorationImage(
                image: AssetImage('assets/images/blurred_bg.png'),
                fit: BoxFit.cover,
              ),
            ),
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Results for $playerName',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Genos',
                    ),
                  ),
                ),
                
                // Results table
                Expanded(
                  child: _buildResultsTable(results),
                ),
                
                // Close button
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4D9DE0).withValues(alpha: 0.9),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Close'),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildResultsTable(List results) {
    if (results.isEmpty) {
      return const Center(
        child: Text(
          'No results yet',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontFamily: 'Genos',
          ),
        ),
      );
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          headingRowColor: WidgetStateProperty.all(
            const Color(0xFF4D9DE0).withValues(alpha: 0.3),
          ),
          dataRowColor: WidgetStateProperty.all(
            Colors.white.withValues(alpha: 0.1),
          ),
          headingTextStyle: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontFamily: 'Genos',
          ),
          dataTextStyle: const TextStyle(
            color: Colors.white,
            fontFamily: 'Genos',
          ),
          columns: const [
            DataColumn(
              label: Text('Result'),
              tooltip: 'Game result',
            ),
            DataColumn(
              label: Text('Player Points'),
              tooltip: 'Points scored by player',
            ),
            DataColumn(
              label: Text('Robot Points'),
              tooltip: 'Points scored by robot',
            ),
            DataColumn(
              label: Text('Player Jackpot'),
              tooltip: 'Jackpot points earned by player',
            ),
            DataColumn(
              label: Text('Robot Jackpot'),
              tooltip: 'Jackpot points earned by robot',
            ),
            DataColumn(
              label: Text('Time'),
              tooltip: 'Game duration',
            ),
            DataColumn(
              label: Text('Date'),
              tooltip: 'When the game was played',
            ),
          ],
          rows: results.reversed.map<DataRow>((result) {
            return DataRow(
              cells: [
                DataCell(
                  _buildResultCell(result['result'] ?? ''),
                ),
                DataCell(
                  Text(result['player_points']?.toString() ?? '0'),
                ),
                DataCell(
                  Text(result['program_points']?.toString() ?? '0'),
                ),
                DataCell(
                  Text(result['player_jackpot']?.toString() ?? '0'),
                ),
                DataCell(
                  Text(result['program_jackpot']?.toString() ?? '0'),
                ),
                DataCell(
                  Text(result['elapsed_time']?.toString() ?? '00:00'),
                ),
                DataCell(
                  Text(_formatTiming(result['timing']?.toString() ?? '')),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildResultCell(String result) {
    Color textColor;
    switch (result.toLowerCase()) {
      case 'victory':
        textColor = Colors.green;
        break;
      case 'defeat':
        textColor = Colors.red;
        break;
      default:
        textColor = Colors.white;
    }
    
    return Text(
      result,
      style: TextStyle(
        color: textColor,
        fontWeight: FontWeight.bold,
        fontFamily: 'Genos',
      ),
    );
  }

  String _formatTiming(String timing) {
    try {
      // Parse the datetime string and format it nicely
      final dateTime = DateTime.parse(timing);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')} - ${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year}';
    } catch (e) {
      return timing;
    }
  }
}