import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/game_state_provider.dart';
import 'player_results_dialog.dart';

class PlayerTableDialog extends StatefulWidget {
  const PlayerTableDialog({super.key});

  @override
  State<PlayerTableDialog> createState() => _PlayerTableDialogState();
}

class _PlayerTableDialogState extends State<PlayerTableDialog> {
  final TextEditingController _newPlayerController = TextEditingController();

  @override
  void dispose() {
    _newPlayerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Consumer<GameStateProvider>(
        builder: (context, gameState, child) {
          final players = Map<String, dynamic>.from(gameState.playersData['players'] ?? {});
          
          return Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              image: const DecorationImage(
                image: AssetImage('assets/images/blurred_bg.png'),
                fit: BoxFit.cover,
              ),
            ),
            child: Column(
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(16),
                  child: const Text(
                    'Select or Add Player',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Genos',
                    ),
                  ),
                ),
                
                // Player list
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: ListView.builder(
                      itemCount: players.length,
                      itemBuilder: (context, index) {
                        final playerName = players.keys.elementAt(index);
                        return _buildPlayerListItem(context, gameState, playerName);
                      },
                    ),
                  ),
                ),
                
                // Add new player section
                _buildAddPlayerSection(context, gameState),
                
                // Close button
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4D9DE0).withValues(alpha: 0.9),
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Close'),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPlayerListItem(BuildContext context, GameStateProvider gameState, String playerName) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          // Player select button
          Expanded(
            flex: 3,
            child: GestureDetector(
              onTap: () => _selectPlayer(context, gameState, playerName),
              onLongPress: () => _confirmDeletePlayer(context, gameState, playerName),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF4D9DE0).withValues(alpha: 0.9),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                ),
                child: Text(
                  playerName.toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Genos',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          
          // View results button
          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: () => _showResults(context, gameState, playerName),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF4D9DE0).withValues(alpha: 0.7),
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: const Text(
                  'View Results',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Genos',
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddPlayerSection(BuildContext context, GameStateProvider gameState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Text input for new player name
          TextField(
            controller: _newPlayerController,
            maxLength: 20,
            decoration: InputDecoration(
              hintText: 'Enter player name',
              hintStyle: const TextStyle(color: Colors.white70),
              filled: true,
              fillColor: Colors.white.withValues(alpha: 0.1),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF4D9DE0)),
              ),
              counterStyle: const TextStyle(color: Colors.white70),
            ),
            style: const TextStyle(color: Colors.white),
            onSubmitted: (_) => _addNewPlayer(context, gameState),
          ),
          
          const SizedBox(height: 10),
          
          // Add player button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _addNewPlayer(context, gameState),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4D9DE0).withValues(alpha: 0.9),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Add New Player',
                style: TextStyle(
                  fontSize: 16,
                  fontFamily: 'Genos',
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _selectPlayer(BuildContext context, GameStateProvider gameState, String playerName) {
    gameState.selectPlayer(playerName);
    Navigator.pop(context);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected player: $playerName'),
        backgroundColor: const Color(0xFF4D9DE0),
      ),
    );
  }

  void _addNewPlayer(BuildContext context, GameStateProvider gameState) async {
    final playerName = _newPlayerController.text.trim();
    if (playerName.isNotEmpty) {
      await gameState.addNewPlayer(playerName);
      _newPlayerController.clear();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Added and selected player: $playerName'),
          backgroundColor: Colors.green,
        ),
      );
      
      Navigator.pop(context);
    }
  }

  void _showResults(BuildContext context, GameStateProvider gameState, String playerName) {
    showDialog(
      context: context,
      builder: (context) => PlayerResultsDialog(playerName: playerName),
    );
  }

  void _confirmDeletePlayer(BuildContext context, GameStateProvider gameState, String playerName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.transparent,
        content: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFF8B8CB6).withValues(alpha: 0.97),
            borderRadius: BorderRadius.circular(15),
            image: const DecorationImage(
              image: AssetImage('assets/images/blurred_bg.png'),
              fit: BoxFit.cover,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Confirm player $playerName deletion?',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontFamily: 'Genos',
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 20),
              
              Row(
                children: [
                  // Confirm button
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () async {
                        await gameState.deletePlayer(playerName);
                        Navigator.pop(context); // Close confirm dialog
                        Navigator.pop(context); // Close player table
                        
                        // Show updated player table
                        showDialog(
                          context: context,
                          builder: (context) => const PlayerTableDialog(),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF4D9DE0).withValues(alpha: 0.9),
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Confirm'),
                    ),
                  ),
                  
                  const SizedBox(width: 10),
                  
                  // Cancel button
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF4D9DE0).withValues(alpha: 0.9),
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}